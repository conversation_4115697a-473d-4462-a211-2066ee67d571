/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.admincommandhandlers;

import static club.projectessence.gameserver.model.itemcontainer.Inventory.MAX_ADENA;

import java.awt.Color;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.mariadb.jdbc.MariaDbPoolDataSource;

import ai.bosses.Octavis.Octavis;
import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.LoginServerThread;
import club.projectessence.gameserver.ai.CtrlEvent;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.SpawnTable;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.data.sql.CrestTable;
import club.projectessence.gameserver.data.xml.ExperienceData;
import club.projectessence.gameserver.data.xml.InitialEquipmentData;
import club.projectessence.gameserver.data.xml.InitialShortcutData;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.data.xml.PlayerTemplateData;
import club.projectessence.gameserver.data.xml.RandomCraftData;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.data.xml.SkillTreeData;
import club.projectessence.gameserver.data.xml.SpawnData;
import club.projectessence.gameserver.enums.NpcInfoType;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.handler.AdminCommandHandler;
import club.projectessence.gameserver.handler.IAdminCommandHandler;
import club.projectessence.gameserver.instancemanager.DailyTaskManager;
import club.projectessence.gameserver.instancemanager.GlobalVariables;
import club.projectessence.gameserver.instancemanager.PrivateStoreHistoryManager;
import club.projectessence.gameserver.instancemanager.QuestManager;
import club.projectessence.gameserver.instancemanager.QueueManager;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.model.CharSelectInfoPackage;
import club.projectessence.gameserver.model.ExtractableProduct;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Shortcut;
import club.projectessence.gameserver.model.SkillLearn;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.TradeItem;
import club.projectessence.gameserver.model.TradeList;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.appearance.PlayerAppearance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.stat.PlayerStat;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.actor.templates.PlayerTemplate;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.events.Containers;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerCreate;
import club.projectessence.gameserver.model.holders.RandomCraftRewardHolder;
import club.projectessence.gameserver.model.items.EtcItem;
import club.projectessence.gameserver.model.items.PlayerItemTemplate;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.olympiad.Olympiad;
import club.projectessence.gameserver.model.punishment.PunishmentAffect;
import club.projectessence.gameserver.model.punishment.PunishmentType;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.spawns.NpcSpawnTemplate;
import club.projectessence.gameserver.model.stats.BaseStat;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.network.ConnectionState;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.CharCreateOk;
import club.projectessence.gameserver.network.serverpackets.CharSelectionInfo;
import club.projectessence.gameserver.network.serverpackets.EnchantResult;
import club.projectessence.gameserver.network.serverpackets.ExItemAnnounce;
import club.projectessence.gameserver.network.serverpackets.ExServerPrimitive;
import club.projectessence.gameserver.network.serverpackets.InventoryUpdate;
import club.projectessence.gameserver.network.serverpackets.PrivateStoreMsgSell;
import club.projectessence.gameserver.network.serverpackets.RestartResponse;
import club.projectessence.gameserver.network.serverpackets.TestPacket;
import club.projectessence.gameserver.taskmanager.AdditionalSaveTaskManager;
import club.projectessence.gameserver.taskmanager.AttackableThinkTaskManager;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;
import club.projectessence.gameserver.taskmanager.autoplay.AutoUseTaskManager;
import club.projectessence.gameserver.util.Broadcast;
import club.projectessence.gameserver.util.GeoUtils;

/**
 * <AUTHOR>
 */
public class AdminDebug implements IAdminCommandHandler
{
	private static final Logger			LOGGER			= Logger.getLogger(AdminDebug.class.getName());
	private static final String[]		ADMIN_COMMANDS	=
	{
		"admin_debug"
	};
	private static ScheduledFuture<?>	_gcTask			= null;
	
	@Override
	public boolean useAdminCommand(String command, PlayerInstance activeChar)
	{
		if (command.startsWith("admin_debug"))
		{
			StringTokenizer st = new StringTokenizer(command, " ");
			st.nextToken();
			if (st.hasMoreTokens())
			{
				String s = st.nextToken();
				if (s.equals("chestdat"))
				{
					EtcItem item = (EtcItem) ItemTable.getInstance().getTemplate(Integer.parseInt(st.nextToken()));
					double chest_chance = 0;
					System.out.println();
					System.out.print("{{2;{");
					for (int i = 0; i < item.getExtractableItems().size(); i++)
					{
						ExtractableProduct extr = item.getExtractableItems().get(i);
						System.out.print("{" + extr.getId() + ";" + extr.getMax() + ";0}");
						if (i != (item.getExtractableItems().size() - 1))
						{
							System.out.print(";");
						}
						chest_chance += extr.getChance();
					}
					System.out.println("}}}\n" + String.format("%.5f", chest_chance / 10000000000000000L));
				}
				else if (s.equals("ban_target_ip_acc"))
				{
					boolean announce = false;
					if (st.hasMoreTokens())
					{
						announce = true;
					}
					String targetIp = activeChar.getTarget().getActingPlayer().getClient().getHostAddress();
					final List<PlayerInstance> players = new ArrayList<>(World.getInstance().getPlayers());
					players.sort(Comparator.comparingLong(PlayerInstance::getUptime));
					for (PlayerInstance player : players)
					{
						final GameClient client = player.getClient();
						if ((client == null) || client.isDetached() || !player.isOnline())
						{
							continue;
						}
						String ip = client.getHostAddress();
						if (!ip.equals(targetIp))
						{
							continue;
						}
						if (announce)
						{
							AdminCommandHandler.getInstance().useAdminCommand(activeChar, "admin_announce " + player.getName() + " Banned.", false);
						}
						LOGGER.info("Banned: " + player + "[Acc: " + player.getAccountName() + "]");
						AdminCommandHandler.getInstance().useAdminCommand(activeChar, String.format("admin_punishment_add %s %s %s %s %s", player.getAccountName(), PunishmentAffect.ACCOUNT, PunishmentType.BAN, 0, "Banned by admin"), false);
					}
					LOGGER.info("ADD BAN IP: " + targetIp);
					return true;
				}
				else if (s.equals("fakechest"))
				{
					if ((activeChar.getTarget() == null) || !activeChar.getTarget().isPlayer())
					{
						activeChar.sendPacket(SystemMessageId.INVALID_TARGET);
						return false;
					}
					int obtainedId = Integer.parseInt(st.nextToken());
					Broadcast.toAllOnlinePlayers(new ExItemAnnounce(activeChar.getTarget().getActingPlayer(), obtainedId, 110017, ExItemAnnounce.ItemAnnounceType.LOOT_BOX));
				}
				else if (s.equals("destroyclan"))
				{
					ClanTable.getInstance().destroyClan(Integer.parseInt(st.nextToken()));
				}
				else if (s.equals("debuff"))
				{
					SkillData.getInstance().getSkill(45477, 1).applyEffects(activeChar, activeChar);
				}
				else if (s.equals("migrate_hennas"))
				{
					/**
					 * ########################################################## 1) Table DDl has been already added to s2 & s1 ########################################################## 2) 1 time queries to be executed INSERT INTO henna_migration (charId, itemId, itemCount, adenaCount) SELECT
					 * charId, henna_migration_model.itemId, henna_migration_model.itemCount, henna_migration_model.adenaCount from character_hennas INNER JOIN henna_migration_model ON character_hennas.symbol_id = henna_migration_model.henna; DELETE FROM character_hennas WHERE symbol_id IN (SELECT
					 * henna FROM henna_migration_model); ########################################################## 3) Execute command //debug migrate_hennas
					 */
					final String SELECT = "SELECT charId, itemId, sum(itemCount) AS itemCount, sum(adenaCount) AS adenaCount FROM henna_migration GROUP BY charId, itemId";
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement(SELECT))
					{
						try (ResultSet rset = statement.executeQuery())
						{
							while (rset.next())
							{
								final int charId = rset.getInt("charId");
								final int itemId = rset.getInt("itemId");
								final long itemCount = rset.getLong("itemCount");
								final long adenaCount = rset.getLong("adenaCount");
								final PlayerInstance player = PlayerInstance.load(charId);
								if (player != null)
								{
									player.getWarehouse().addItem("hennas_migration", itemId, itemCount, activeChar, null);
									player.addAdena("hennas_migration", adenaCount, activeChar, false);
									player.deleteMe(); // Enough to store item
								}
								else
								{
									LOGGER.log(Level.SEVERE, "Could not restore char: " + charId);
								}
							}
						}
					}
					catch (Exception e)
					{
						LOGGER.log(Level.SEVERE, "Could not migrate hennas: " + e.getMessage(), e);
					}
				}
				else if (s.equals("migrate_title_colors"))
				{
					/**
					 * ########################################################## 1) Table DDL to be added CREATE TABLE title_reset ( charId int not null primary key, itemId int null ); ########################################################## 2) 1 time queries to be executed INSERT INTO
					 * title_reset (charId, itemId) SELECT charId, CASE WHEN title_color = 9671679 THEN 13307 WHEN title_color = 8145404 THEN 13307 WHEN title_color = 9959676 THEN 13307 WHEN title_color = 16423662 THEN 13307 WHEN title_color = 16735635 THEN 13307 WHEN title_color = 64672 THEN 13307
					 * WHEN title_color = 10528257 THEN 13307 WHEN title_color = 7903407 THEN 13307 WHEN title_color = 4743829 THEN 13307 WHEN title_color = 10066329 THEN 13307 WHEN title_color = 55295 THEN 94764 WHEN title_color = 42495 THEN 94764 WHEN title_color = 14695660 THEN 94764 WHEN
					 * title_color = 3224054 THEN 94764 WHEN title_color = 16766720 THEN 94764 END AS itemId FROM characters WHERE title_color IN (9671679, 8145404, 9959676, 16423662, 16735635, 64672, 10528257, 7903407, 4743829, 10066329, 55295, 42495, 14695660, 3224054, 16766720); UPDATE characters
					 * SET title_color = 15530402; ########################################################## 3) Execute command //debug migrate_title_colors
					 */
					final String SELECT = "SELECT charId, itemId FROM title_reset";
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement(SELECT))
					{
						try (ResultSet rset = statement.executeQuery())
						{
							while (rset.next())
							{
								final int charId = rset.getInt("charId");
								final int itemId = rset.getInt("itemId");
								final PlayerInstance player = PlayerInstance.load(charId);
								if (player != null)
								{
									player.getWarehouse().addItem("title_migration", itemId, 1, activeChar, null);
									player.deleteMe(); // Enough to store item
								}
								else
								{
									LOGGER.log(Level.SEVERE, "Could not restore char: " + charId);
								}
							}
						}
					}
					catch (Exception e)
					{
						LOGGER.log(Level.SEVERE, "Could not migrate titles: " + e.getMessage(), e);
					}
				}
				else if (s.equals("clients"))
				{
					activeChar.sendMessage("Clients: " + LoginServerThread.getInstance().getIngameClientsCount());
				}
				else if (s.equals("waiting_clients"))
				{
					activeChar.sendMessage("Waiting clients: " + LoginServerThread.getInstance().getWaitingClientsCount());
				}
				else if (s.equals("testhec"))
				{
					final Creature target = (Creature) activeChar.getTarget();
					System.out.println("STR bonus: " + BaseStat.values()[(byte) target.getStat().getValue(Stat.STAT_BONUS_SKILL_CRITICAL)].calcBonus(target));
					System.out.println("skill crit bonuses: " + target.getStat().getValue(Stat.SKILL_CRITICAL_PROBABILITY, 1));
					System.out.println("Final: " + (20 * BaseStat.values()[(byte) target.getStat().getValue(Stat.STAT_BONUS_SKILL_CRITICAL)].calcBonus(target) * target.getStat().getValue(Stat.SKILL_CRITICAL_PROBABILITY, 1)));
					System.out.println("Phys Skill Evasion: " + target.getStat().getSkillEvasionTypeValue(0));
					System.out.println("Magic Skill Evasion: " + target.getStat().getSkillEvasionTypeValue(1));
					System.out.println(((2 * target.getStat().getValue(Stat.SHOTS_BONUS)) + "\n" + (target.getStat().getValue(Stat.SOULSHOT_DAMAGE, 1) - 1))); // 2.04 for dual weapon?
				}
				else if (s.equals("testpet"))
				{
					ItemInstance test = activeChar.getInventory().getItemByItemId(2500);
					if (test != null)
					{
						InventoryUpdate iu = new InventoryUpdate();
						iu.addRemovedItem(test);
						activeChar.sendPacket(iu);
					}
				}
				else if (s.equals("shop1"))
				{
					PrivateStoreHistoryManager.getInstance().restore();
				}
				else if (s.equals("shop2"))
				{
					System.out.println("Base List:");
					PrivateStoreHistoryManager.getInstance().getHistory(true).forEach(item -> System.out.println(item));
					System.out.println("Base List Light:");
					PrivateStoreHistoryManager.getInstance().getHistory(false).forEach(item -> System.out.println(item));
					System.out.println("Highest List:");
					PrivateStoreHistoryManager.getInstance().getTopHighestItem().forEach(item -> System.out.println(item));
					System.out.println("Most List:");
					PrivateStoreHistoryManager.getInstance().getTopMostItem().forEach(item -> System.out.println(item));
				}
				else if (s.equals("shop3"))
				{
					activeChar.getSellList().clear();
					activeChar.getSellList().updateItems();
					List<TradeItem> itemList = activeChar.getInventory().getAvailableItems(activeChar.getSellList()).stream().toList();
					Set<TradeItem> sellList = activeChar.getSellList().getItems();
					for (TradeItem ti : sellList)
					{
						itemList.add(ti);
					}
					final TradeList tradeList = activeChar.getSellList();
					tradeList.clear();
					tradeList.setPackaged(false);
					Item[] _items = null;
					_items = new Item[itemList.size()];
					for (int i = 0; i < itemList.size(); i++)
					{
						_items[i] = new Item(itemList.get(i).getObjectId(), itemList.get(i).getCount(), itemList.get(i).getPrice());
					}
					for (Item i : _items)
					{
						if (!i.addToTradeList(tradeList))
						{
							LOGGER.info("Warning!! Character " + activeChar.getName() + " of account " + activeChar.getAccountName() + " tried to set price more than " + MAX_ADENA + " adena in Private Store - Sell.");
							return true;
						}
						final ItemInstance item = activeChar.getInventory().getItemByObjectId(i._objectId);
						if ((item == null) || (item.getCount() < i._count))
						{
							activeChar.sendPacket(SystemMessageId.INCORRECT_ITEM_COUNT);
							activeChar.setPrivateStoreType(PrivateStoreType.NONE);
							return true;
						}
					}
					activeChar.sitDown();
					activeChar.setPrivateStoreType(PrivateStoreType.SELL);
					activeChar.broadcastUserInfo();
					activeChar.broadcastPacket(new PrivateStoreMsgSell(activeChar));
				}
				else if (s.equals("test"))
				{
					activeChar.sendPacket(new EnchantResult(EnchantResult.FAIL, 0, 0, new int[]
					{
						0,
						0,
						0,
					}));
				}
				else if (s.equals("test2"))
				{
					SpawnData.getInstance().getSpawns().stream().filter(spawnTemplate -> (spawnTemplate.getName() != null) && spawnTemplate.getName().equals("Darion_Eigis")).forEach(spawnTemplate -> spawnTemplate.getGroups().forEach(holder -> holder.spawnAll()));
				}
				else if (s.equals("antharas_dv_on"))
				{
					SpawnData.getInstance().getSpawns().stream().filter(spawnTemplate -> (spawnTemplate.getName() != null) && spawnTemplate.getName().equals("Dragon Valley No Antharas")).forEach(spawnTemplate -> spawnTemplate.getGroups().forEach(holder -> holder.despawnAll()));
					SpawnData.getInstance().getSpawns().stream().filter(spawnTemplate -> (spawnTemplate.getName() != null) && spawnTemplate.getName().equals("Dragon Valley Antharas")).forEach(spawnTemplate -> spawnTemplate.getGroups().forEach(holder -> holder.despawnAll()));
					SpawnData.getInstance().getSpawns().stream().filter(spawnTemplate -> (spawnTemplate.getName() != null) && spawnTemplate.getName().equals("Dragon Valley Antharas")).forEach(spawnTemplate -> spawnTemplate.getGroups().forEach(holder -> holder.spawnAll()));
				}
				else if (s.equals("antharas_dv_on"))
				{
					SpawnData.getInstance().getSpawns().stream().filter(spawnTemplate -> (spawnTemplate.getName() != null) && spawnTemplate.getName().equals("Dragon Valley No Antharas")).forEach(spawnTemplate -> spawnTemplate.getGroups().forEach(holder -> holder.despawnAll()));
					SpawnData.getInstance().getSpawns().stream().filter(spawnTemplate -> (spawnTemplate.getName() != null) && spawnTemplate.getName().equals("Dragon Valley Antharas")).forEach(spawnTemplate -> spawnTemplate.getGroups().forEach(holder -> holder.despawnAll()));
					SpawnData.getInstance().getSpawns().stream().filter(spawnTemplate -> (spawnTemplate.getName() != null) && spawnTemplate.getName().equals("Dragon Valley No Antharas")).forEach(spawnTemplate -> spawnTemplate.getGroups().forEach(holder -> holder.spawnAll()));
				}
				else if (s.equals("new_spawn"))
				{
					final int id = Integer.parseInt(st.nextToken());
					System.out.println("<npc id=\"" + id + "\" x=\"" + activeChar.getX() + "\" y=\"" + activeChar.getY() + "\" z=\"" + activeChar.getZ() + "\" heading=\"" + activeChar.getHeading() + "\" />");
				}
				else if (s.equals("closeloginsocket"))
				{
					LoginServerThread.getInstance().closeSocket();
				}
				else if (s.equals("tryrestartloginthread"))
				{
					try
					{
						LoginServerThread.getInstance().interrupt();
					}
					catch (Exception e)
					{
						e.printStackTrace();
						LOGGER.warning("Fail interrupt");
					}
					try
					{
						LoginServerThread.getInstance().start();
					}
					catch (Exception e)
					{
						e.printStackTrace();
						LOGGER.warning("Fail Start");
					}
				}
				else if (s.equals("restartloginthread"))
				{
					try
					{
						LoginServerThread.restart();
					}
					catch (Exception e)
					{
						e.printStackTrace();
						LOGGER.warning("Fail restart");
					}
				}
				else if (s.equals("autoplay"))
				{
					AutoPlayTaskManager.getInstance().printDebug();
					AutoUseTaskManager.getInstance().printDebug();
				}
				else if (s.equals("attackabletask"))
				{
					AttackableThinkTaskManager.getInstance().printDebug();
				}
				else if (s.equals("randomcraftini"))
				{
					String fullStr = "";
					for (RandomCraftRewardHolder rc : RandomCraftData.getInstance().getRewards())
					{
						fullStr += rc.getItemId() + ";";
					}
					fullStr = fullStr.substring(0, fullStr.length() - 1);
					String firstHalf = "";
					String secondHalf = "";
					for (int i = fullStr.length() / 2; i < fullStr.length(); i++)
					{
						if (fullStr.charAt(i) == ';')
						{
							firstHalf = fullStr.substring(0, i);
							secondHalf = fullStr.substring(i, fullStr.length());
							break;
						}
					}
					System.out.println("RANDOM_CRAFT_ITEM_LIST1=" + firstHalf);
					System.out.println("RANDOM_CRAFT_ITEM_LIST2=" + secondHalf);
				}
				else if (s.equals("testdeath"))
				{
					if ((activeChar.getTarget() != null) && (activeChar.getTarget().getActingPlayer() != null))
					{
						final PlayerInstance plr = activeChar.getTarget().getActingPlayer();
						if (plr.isDead())
						{
							plr.getAI().notifyEvent(CtrlEvent.EVT_DEAD);
						}
					}
				}
				else if (s.equals("dwarfroyale"))
				{
					try (Connection conn = DatabaseFactory.getConnection(); PreparedStatement ps = conn.prepareStatement("DELETE FROM `collections` WHERE `accountName`=?"))
					{
						for (PlayerInstance player : World.getInstance().getVisibleObjects(activeChar, PlayerInstance.class))
						{
							try
							{
								if (player.getName().contains("__"))
								{
									continue;
								}
								final String accountName = player.getAccountName();
								final String oldCharName = player.getName();
								final Location oldCharLoc = player.getLocation();
								player.getCollections().clear();
								player.setPrimePoints(0);
								final GameClient client = player.getClient();
								Disconnection.of(client, player).restart();
								ps.setString(1, accountName);
								ps.execute();
								// relog
								client.setConnectionState(ConnectionState.AUTHENTICATED);
								client.sendPacket(RestartResponse.TRUE);
								CharSelectionInfo cl = new CharSelectionInfo(client.getAccountName(), client.getSessionKey().playOkID1);
								client.sendPacket(cl);
								client.setCharactersInfo(cl.getCharInfo());
								client._canSendPackets = false;
								ThreadPool.get().schedule(() -> // create char
								{
									PlayerTemplate template = PlayerTemplateData.getInstance().getTemplate(57);
									final String newCharName = "__" + oldCharName;
									final PlayerInstance newChar = PlayerInstance.create(template, client.getAccountName(), newCharName, new PlayerAppearance((byte) 0, (byte) 0, (byte) 0, false));
									newChar.getStat().setLevel(40);
									newChar.giveAvailableSkills(true, true);
									newChar.setCurrentHp(newChar.getMaxHp());
									newChar.setCurrentMp(newChar.getMaxMp());
									newChar.setCurrentCp(newChar.getMaxCp());
									// init new char start
									World.getInstance().addObject(newChar);
									if (Config.STARTING_ADENA > 0)
									{
										newChar.addAdena("Init", Config.STARTING_ADENA, null, false);
									}
									template = newChar.getTemplate();
									if (Config.CUSTOM_STARTING_LOC)
									{
										final Location createLoc = new Location(Config.CUSTOM_STARTING_LOC_X, Config.CUSTOM_STARTING_LOC_Y, Config.CUSTOM_STARTING_LOC_Z);
										newChar.setXYZInvisible(createLoc.getX(), createLoc.getY(), createLoc.getZ());
									}
									else
									{
										final Location createLoc = template.getCreationPoint();
										newChar.setXYZInvisible(createLoc.getX(), createLoc.getY(), createLoc.getZ());
									}
									newChar.setTitle("", false, false);
									if (Config.ENABLE_SAYHA_GRACE)
									{
										newChar.setSayhaGracePoints(Math.min(Config.STARTING_SAYHA_GRACE_POINTS, PlayerStat.MAX_SAYHA_GRACE_POINTS), true);
									}
									if (Config.STARTING_LEVEL > 1)
									{
										newChar.getStat().addLevel((byte) (Config.STARTING_LEVEL - 1));
									}
									if (Config.STARTING_SP > 0)
									{
										newChar.getStat().addSp(Config.STARTING_SP);
									}
									final List<PlayerItemTemplate> initialItems = InitialEquipmentData.getInstance().getEquipmentList(53);
									if (initialItems != null)
									{
										for (PlayerItemTemplate ie : initialItems)
										{
											if (ie.getId() == 10650)
											{
												continue;
											}
											final ItemInstance item = newChar.getInventory().addItem("Init", ie.getId(), ie.getCount(), newChar, null);
											if (item == null)
											{
												LOGGER.warning("Could not create item during char creation: itemId " + ie.getId() + ", amount " + ie.getCount() + ".");
												continue;
											}
											if (item.isEquipable() && ie.isEquipped())
											{
												newChar.getInventory().equipItem(item);
											}
										}
									}
									newChar.getInventory().addItem("Init", 91927, 5000, newChar, null); // Soulshots
									newChar.getInventory().addItem("Init", 91930, 5000, newChar, null); // Blessed Spiritshot
									for (SkillLearn skill : SkillTreeData.getInstance().getAvailableSkills(newChar, newChar.getClassId(), false, true))
									{
										newChar.addSkill(SkillData.getInstance().getSkill(skill.getSkillId(), skill.getSkillLevel()), true);
									}
									// Register all shortcuts for actions, skills and items for this new character.
									InitialShortcutData.getInstance().registerAllShortcuts(newChar);
									EventDispatcher.getInstance().notifyEvent(new OnPlayerCreate(newChar, newChar.getObjectId(), newChar.getName(), client), Containers.Players());
									final Location newCharLoc = new Location(oldCharLoc.getX(), oldCharLoc.getY(), oldCharLoc.getZ() + 15);
									newChar.setLocation(newCharLoc);
									newChar.setOnlineStatus(true, false);
									Disconnection.of(client, newChar).restart();
									CharSelectionInfo cl2 = new CharSelectionInfo(client.getAccountName(), client.getSessionKey().playOkID1);
									client.sendPacket(CharCreateOk.STATIC_PACKET);
									client.sendPacket(cl2);
									client.setCharactersInfo(cl2.getCharInfo());
									ThreadPool.get().schedule(() -> // login
									{
										client._canSendPackets = true;
										int i = 0;
										for (CharSelectInfoPackage csip : cl2.getCharInfo())
										{
											if (csip.getName().equals(newCharName))
											{
												client.setCharSlot(i);
												QueueManager.getInstance().addToQueue(client);
											}
											i++;
										}
									}, 10000);
								}, 10000);
							}
							catch (Exception e)
							{
								LOGGER.warning("Failed: " + player);
								e.printStackTrace();
							}
						}
					}
					catch (Exception e)
					{
						e.printStackTrace();
					}
				}
				else if (s.equals("octavis"))
				{
					final Octavis octavis = (Octavis) QuestManager.getInstance().getScripts().get("Octavis");
					octavis.onTimerEvent("START_STAGE_1", null, null, activeChar);
				}
				else if (s.equals("set_clan_arena"))
				{
					int progress = Integer.parseInt(st.nextToken());
					int SKILL_ID = 1867;
					final Clan clan = activeChar.getTarget().getActingPlayer().getClan();
					GlobalVariables.getInstance().increaseInt("MA_C" + clan.getId(), progress);
					if (progress >= 25) // && clan.getSkills().containsValue(SkillData.getInstance().getSkill(SKILL_ID, 4)))
					{
						Skill skill = SkillData.getInstance().getSkill(SKILL_ID, 5);
						clan.addNewSkill(skill);
					}
					else if (progress >= 20)// && clan.getSkills().containsValue(SkillData.getInstance().getSkill(SKILL_ID, 3)))
					{
						Skill skill = SkillData.getInstance().getSkill(SKILL_ID, 4);
						clan.addNewSkill(skill);
					}
					else if (progress >= 15) // && clan.getSkills().containsValue(SkillData.getInstance().getSkill(SKILL_ID, 2)))
					{
						Skill skill = SkillData.getInstance().getSkill(SKILL_ID, 3);
						clan.addNewSkill(skill);
					}
					else if (progress >= 10)// && clan.getSkills().containsValue(SkillData.getInstance().getSkill(SKILL_ID, 1)))
					{
						Skill skill = SkillData.getInstance().getSkill(SKILL_ID, 2);
						clan.addNewSkill(skill);
					}
					else if (progress >= 5)// && !clan.getSkills().containsValue(SkillData.getInstance().getSkill(SKILL_ID, 1)))
					{
						Skill skill = SkillData.getInstance().getSkill(SKILL_ID, 1);
						clan.addNewSkill(skill);
					}
					activeChar.sendMessage("Set clan arena level of " + clan.getName() + " to " + progress);
				}
				else if (s.equals("lickosomass"))
				{
					Disconnection.of(activeChar).logout(true, true);
					try
					{
						Thread.sleep(5_000);
					}
					catch (InterruptedException e1)
					{
						e1.printStackTrace();
					}
					DailyTaskManager.getInstance().disable();
					AdditionalSaveTaskManager.ENABLED = false;
					final long start = System.currentTimeMillis();
					Map<String, List<PlayerInstance>> accounts = new HashMap<>();
					// black coupon
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("SELECT account_name, charId FROM characters ORDER BY account_name"))
					{
						int i = 0;
						try (ResultSet rset = statement.executeQuery())
						{
							while (rset.next())
							{
								i++;
								if ((i % 500) == 0)
								{
									System.out.println("Loaded " + i + "th character in " + (System.currentTimeMillis() - start));
								}
								final int charId = rset.getInt("charId");
								final PlayerInstance player = PlayerInstance.load(charId);
								final String accountName = rset.getString("account_name");
								List<PlayerInstance> list = accounts.get(accountName);
								if (list == null)
								{
									list = new ArrayList<>();
									accounts.put(accountName, list);
								}
								list.add(player);
							}
						}
					}
					catch (Exception e)
					{
						LOGGER.log(Level.SEVERE, "Could not migrate hennas: " + e.getMessage(), e);
					}
					try (Connection conn = DatabaseFactory.getConnection(); PreparedStatement ps = conn.prepareStatement("INSERT INTO `character_premium_items` VALUES (?,?,?,?,?)"))
					{
						int i = 0;
						for (Entry<String, List<PlayerInstance>> account : accounts.entrySet())
						{
							i++;
							if ((i % 500) == 0)
							{
								System.out.println("Calculated " + i + "th character in " + (System.currentTimeMillis() - start));
							}
							int circletEnchant = 0;
							int beltEnchant = 0;
							int speedEnchant = 0;
							int authorityEnchant = 0;
							PlayerInstance highestLevel = null;
							int lv = -1;
							long exp = -1;
							for (PlayerInstance player : account.getValue())
							{
								if ((player.getLevel() >= lv) && (player.getExp() >= exp))
								{
									lv = player.getLevel();
									exp = player.getExp();
									highestLevel = player;
								}
							}
							for (PlayerInstance player : account.getValue())
							{
								if (player != null)
								{
									final int CIRCLET_OF_HERO = 94166;
									final int DRAGON_BELT = 91862;
									final int BLESSED_DRAGON_BELT = 94621;
									final int SPEED_TALISMAN = 92403;
									final int AUTHORITY_TALISMAN = 91601;
									for (ItemInstance item : player.getInventory().getItemsByItemId(CIRCLET_OF_HERO))
									{
										circletEnchant = item.getEnchantLevel() > circletEnchant ? item.getEnchantLevel() : circletEnchant;
									}
									for (ItemInstance item : player.getWarehouse().getItemsByItemId(CIRCLET_OF_HERO))
									{
										circletEnchant = item.getEnchantLevel() > circletEnchant ? item.getEnchantLevel() : circletEnchant;
									}
									for (ItemInstance item : player.getFreight().getItemsByItemId(CIRCLET_OF_HERO))
									{
										circletEnchant = item.getEnchantLevel() > circletEnchant ? item.getEnchantLevel() : circletEnchant;
									}
									for (ItemInstance item : player.getInventory().getItemsByItemId(DRAGON_BELT))
									{
										beltEnchant = item.getEnchantLevel() > beltEnchant ? item.getEnchantLevel() : beltEnchant;
									}
									for (ItemInstance item : player.getWarehouse().getItemsByItemId(DRAGON_BELT))
									{
										beltEnchant = item.getEnchantLevel() > beltEnchant ? item.getEnchantLevel() : beltEnchant;
									}
									for (ItemInstance item : player.getFreight().getItemsByItemId(DRAGON_BELT))
									{
										beltEnchant = item.getEnchantLevel() > beltEnchant ? item.getEnchantLevel() : beltEnchant;
									}
									//
									for (ItemInstance item : player.getInventory().getItemsByItemId(BLESSED_DRAGON_BELT))
									{
										beltEnchant = item.getEnchantLevel() > beltEnchant ? item.getEnchantLevel() : beltEnchant;
									}
									for (ItemInstance item : player.getWarehouse().getItemsByItemId(BLESSED_DRAGON_BELT))
									{
										beltEnchant = item.getEnchantLevel() > beltEnchant ? item.getEnchantLevel() : beltEnchant;
									}
									for (ItemInstance item : player.getFreight().getItemsByItemId(BLESSED_DRAGON_BELT))
									{
										beltEnchant = item.getEnchantLevel() > beltEnchant ? item.getEnchantLevel() : beltEnchant;
									}
									for (ItemInstance item : player.getInventory().getItemsByItemId(AUTHORITY_TALISMAN))
									{
										authorityEnchant = item.getEnchantLevel() > authorityEnchant ? item.getEnchantLevel() : authorityEnchant;
									}
									for (ItemInstance item : player.getWarehouse().getItemsByItemId(AUTHORITY_TALISMAN))
									{
										authorityEnchant = item.getEnchantLevel() > authorityEnchant ? item.getEnchantLevel() : authorityEnchant;
									}
									for (ItemInstance item : player.getFreight().getItemsByItemId(AUTHORITY_TALISMAN))
									{
										authorityEnchant = item.getEnchantLevel() > authorityEnchant ? item.getEnchantLevel() : authorityEnchant;
									}
									for (ItemInstance item : player.getInventory().getItemsByItemId(SPEED_TALISMAN))
									{
										speedEnchant = item.getEnchantLevel() > speedEnchant ? item.getEnchantLevel() : speedEnchant;
									}
									for (ItemInstance item : player.getWarehouse().getItemsByItemId(SPEED_TALISMAN))
									{
										speedEnchant = item.getEnchantLevel() > speedEnchant ? item.getEnchantLevel() : speedEnchant;
									}
									for (ItemInstance item : player.getFreight().getItemsByItemId(SPEED_TALISMAN))
									{
										speedEnchant = item.getEnchantLevel() > speedEnchant ? item.getEnchantLevel() : speedEnchant;
									}
									player.deleteMe(); // Enough to store item
								}
								else
								{
									LOGGER.warning("Could not restore char.");
								}
							}
							int itemId = -1;
							int itemCount = 0;
							if ((circletEnchant < 9) && (beltEnchant < 9) && (authorityEnchant < 9) && (speedEnchant < 9))
							{
								itemId = 100021; // black coupon
								itemCount = 1;
							}
							else if ((circletEnchant < 9) && (beltEnchant < 9))
							{
								itemId = 94852; // black coupon
								itemCount = 2;
							}
							else if ((circletEnchant < 9) || (beltEnchant < 9))
							{
								itemId = 94852; // black coupon
								itemCount = 1;
							}
							if ((itemId > 0) && (itemCount > 0) && (highestLevel != null))
							{
								ps.setInt(1, highestLevel.getObjectId()); // charId
								ps.setInt(2, itemId); // itemId
								ps.setLong(3, itemCount); // itemCount
								ps.setString(4, "Server"); // itemSender
								ps.setLong(5, System.currentTimeMillis() + 604_800_000L);
								ps.addBatch();
							}
						}
						ps.executeBatch();
					}
					catch (SQLException e)
					{
						e.printStackTrace();
					}
					System.out.println("Finished in " + (System.currentTimeMillis() - start) + " ms.");
					System.exit(0);
				}
				else if (s.equals("testcircle"))
				{
					final int RADIUS = 1400;
					final int POINTS = 100;
					final ExServerPrimitive primitive = new ExServerPrimitive("autoFarmRange", activeChar.getX(), activeChar.getY(), activeChar.getZ() + 65535);
					for (int z = 0; z < 5; z++)
					{
						for (int i = 0; i < POINTS; ++i)
						{
							double angle1 = (i * 2 * Math.PI) / POINTS;
							double angle2 = ((i + 1) * 2 * Math.PI) / POINTS;
							int x = (int) (activeChar.getX() + (RADIUS * Math.cos(angle1)));
							int y = (int) (activeChar.getY() + (RADIUS * Math.sin(angle1)));
							int x2 = (int) (activeChar.getX() + (RADIUS * Math.cos(angle2)));
							int y2 = (int) (activeChar.getY() + (RADIUS * Math.sin(angle2)));
							primitive.addLine(Color.YELLOW, x, y, activeChar.getZ() + (z * 100), x2, y2, activeChar.getZ() + (z * 100));
						}
					}
					int radius = 60;
					int x = activeChar.getX();
					int x1 = (int) (x + Math.sqrt((Math.pow(radius, 2)) / 2));
					int x2 = (int) (x - Math.sqrt((Math.pow(radius, 2)) / 2));
					int y = activeChar.getY();
					int y1 = (int) (y + Math.sqrt((Math.pow(radius, 2)) / 2));
					int y2 = (int) (y - Math.sqrt((Math.pow(radius, 2)) / 2));
					int z = activeChar.getZ();
					primitive.addLine(Color.MAGENTA, x1, y1, z, x2, y2, z);
					primitive.addLine(Color.MAGENTA, x2, y1, z, x1, y2, z);
					primitive.addLine(Color.MAGENTA, x, y, z, x, y, z + radius);
					activeChar.sendPacket(primitive);
				}
				else if (s.equals("testnode"))
				{
					System.out.println("\t\t\t\t<node x=\"" + activeChar.getX() + "\" y=\"" + activeChar.getY() + "\" />");
					activeChar.dropItem("test", activeChar.getInventory().getAdenaInstance().getObjectId(), 1, activeChar.getX(), activeChar.getY(), activeChar.getZ(), null, false, false);
				}
				else if (s.equals("testmask"))
				{
					final byte[] DEFAULT_FLAG_ARRAY =
					{
						(byte) 0x80,
						0x40,
						0x20,
						0x10,
						0x08,
						0x04,
						0x02,
						0x01
					};
					byte[] _masks = new byte[]
					{
						(byte) 0xFD,
						(byte) 0xBF,
						(byte) 0x5F,
						(byte) 0xF3,
						(byte) 0xEC
					};
					// ED
					int mask = NpcInfoType.TITLE.getMask(); // check if this is in _masks
					System.out.println((_masks[mask >> 3] & DEFAULT_FLAG_ARRAY[mask & 7]) != 0);
				}
				else if (s.equals("test21"))
				{
					System.out.println(activeChar.getAutoUseSettings().getAutoPotionItems());
				}
				else if (s.equals("test3"))
				{
					final Shortcut sc = activeChar.getShortCut(AutoUseTaskManager.SHORTCUT_HP % 12, AutoUseTaskManager.SHORTCUT_HP / 12);
					System.out.println(activeChar.getInventory().getItemByObjectId(sc.getId()));
				}
				else if (s.equals("test4"))
				{
					SpawnData.getInstance().getSpawns().forEach(spawnTemplate -> spawnTemplate.getGroupsByName("toi_reinforced_mobs").forEach(holder -> holder.despawnAll()));
				}
				else if (s.equals("testconfig"))
				{
					Config.TEST = !Config.TEST;
					activeChar.sendMessage("Config.TEST: " + Config.TEST);
				}
				else if (s.equals("geo"))
				{
					GeoUtils.findNearestRedSquare(activeChar);
				}
				else if (s.equals("gc_task_start"))
				{
					if (_gcTask != null)
					{
						LOGGER.info("GC TASK IS ALREADY RUNNING");
						activeChar.sendMessage("GC TASK IS ALREADY RUNNING");
						return true;
					}
					LOGGER.info("Starting GC Task");
					activeChar.sendMessage("Starting GC Task");
					_gcTask = ThreadPool.get().scheduleAtFixedRate(() ->
					{
						long start = System.currentTimeMillis();
						LOGGER.info("Starting GC");
						System.gc();
						LOGGER.info("Finished GC in " + (System.currentTimeMillis() - start) + " ms.");
					}, 500, 60 * 60 * 1000);
				}
				else if (s.equals("gc_task_stop"))
				{
					if (_gcTask == null)
					{
						LOGGER.info("GC TASK IS NOT RUNNING");
						activeChar.sendMessage("GC TASK IS NOT RUNNING");
						return true;
					}
					LOGGER.info("Stopping GC Task");
					activeChar.sendMessage("Stopping GC Task");
					_gcTask.cancel(false);
					_gcTask = null;
				}
				else if (s.equals("onReset"))
				{
					DailyTaskManager.getInstance().onManualReset();
				}
				else if (s.equals("viprewardreset"))
				{
					DailyTaskManager.getInstance().onManualVIPRewardDailyReset();
				}
				else if (s.equals("rankmanager"))
				{
					RankManager.getInstance().dailyUpdate();
				}
				else if (s.equals("savechars"))
				{
					long startTime = System.currentTimeMillis();
					World.getInstance().getPlayers().stream().forEach(PlayerInstance::autoSave);
					LOGGER.info("Saving all online Players(" + World.getInstance().getPlayers().size() + ") in " + (System.currentTimeMillis() - startTime) + " ms.");
				}
				else if (s.equals("onSave"))
				{
					DailyTaskManager.getInstance().onManualSave();
				}
				else if (s.equals("steadybox_add"))
				{
					activeChar.getSteadyBox().addNewBox(false);
				}
				else if (s.equals("steadybox_info"))
				{
					activeChar.getSteadyBox().printDetails();
				}
				else if (s.equals("dc_all"))
				{
					for (PlayerInstance player : World.getInstance().getPlayers())
					{
						if (player == null)
						{
							continue;
						}
						Disconnection.of(player).logout(true, false);
					}
				}
				else if (s.startsWith("dc_below_level"))
				{
					int minLv = Integer.parseInt(st.nextToken());
					for (PlayerInstance player : World.getInstance().getPlayers())
					{
						if ((player == null) || player.isInOfflineMode())
						{
							continue;
						}
						if (player.getLevel() < minLv)
						{
							Disconnection.of(player).logout(true, false);
						}
					}
				}
				else if (s.startsWith("dc_below_level_no_pa"))
				{
					int minLv = Integer.parseInt(st.nextToken());
					for (PlayerInstance player : World.getInstance().getPlayers())
					{
						if ((player == null) || player.hasPremiumStatus() || player.isInOfflineMode())
						{
							continue;
						}
						if (player.getLevel() < minLv)
						{
							Disconnection.of(player).logout(true, false);
						}
					}
				}
				else if (s.equals("stop_skill_caster"))
				{
					System.out.println("-- Stopping skill casters");
					long start = System.currentTimeMillis();
					for (PlayerInstance player : World.getInstance().getPlayers())
					{
						player.abortAttack();
						player.abortCast();
						player.abortAllSkillCasters();
						for (Summon summon : player.getServitorsAndPets())
						{
							summon.abortAttack();
							summon.abortCast();
							summon.abortAllSkillCasters();
						}
					}
					List<NpcSpawnTemplate> allSpawns = SpawnData.getInstance().getNpcSpawns(t -> true);
					for (NpcSpawnTemplate nst : allSpawns)
					{
						for (Npc npc : nst.getSpawnedNpcs())
						{
							npc.abortAttack();
							npc.abortCast();
							npc.abortAllSkillCasters();
						}
					}
					System.out.println("-- Stopped skill casters: " + (System.currentTimeMillis() - start) + " ms.");
				}
				else if (s.equals("run_gc"))
				{
					System.gc();
				}
				else if (s.equals("make_spawn"))
				{
					int npcId = Rnd.get(22254, 22254);
					try
					{
						final File spawnFile = new File("spawns.xml");
						if (spawnFile.exists()) // update
						{
							final File tempFile = new File(spawnFile.getAbsolutePath().substring(Config.DATAPACK_ROOT.getAbsolutePath().length() + 1).replace('\\', '/') + ".tmp");
							try
							{
								final BufferedReader reader = new BufferedReader(new FileReader(spawnFile));
								final BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile));
								String currentLine;
								while ((currentLine = reader.readLine()) != null)
								{
									writer.write(currentLine + Config.EOL);
								}
								writer.write("\t\t\t<npc id=\"" + npcId + "\" x=\"" + (activeChar.getX() - 10) + "\" y=\"" + (activeChar.getY() - 10) + "\" z=\"" + activeChar.getZ() + "\" heading=\"" + Rnd.get(0, 64000) + "\" respawnTime=\"30sec\" />" + Config.EOL);
								AdminCommandHandler.getInstance().useAdminCommand(activeChar, "admin_spawn_once " + npcId + " 1", false);
								writer.close();
								reader.close();
								spawnFile.delete();
								tempFile.renameTo(spawnFile);
							}
							catch (Exception e)
							{
								e.printStackTrace();
							}
						}
						else
						{
							NpcTemplate template = NpcData.getInstance().getTemplate(npcId);
							String name = template.getName();
							if ((template.getTitle() != null) && !template.getTitle().isBlank())
							{
								name += " " + template.getTitle();
							}
							System.out.println("\t\t\t<npc id=\"" + npcId + "\" x=\"" + (activeChar.getX() - 10) + "\" y=\"" + (activeChar.getY() - 10) + "\" z=\"" + activeChar.getZ() + "\" heading=\"" + Rnd.get(0, 64000) + "\" respawnTime=\"30sec\" /> <!-- " + name + " -->");
							try
							{
								Spawn spawn = new Spawn(NpcData.getInstance().getTemplate(npcId));
								spawn.setXYZ(activeChar.getX() - 10, activeChar.getY() - 10, activeChar.getZ());
								spawn.setHeading(Rnd.get(0, 64000));
								spawn.setAmount(1);
								spawn.setRespawnDelay(0);
								SpawnTable.getInstance().addNewSpawn(spawn, false);
								spawn.init();
								spawn.stopRespawn();
								spawn.getLastSpawn().broadcastInfo();
							}
							catch (ClassNotFoundException | NoSuchMethodException | ClassCastException e)
							{
								e.printStackTrace();
							}
						}
					}
					catch (Exception e)
					{
						e.printStackTrace();
					}
				}
				else if (s.equals("testpacket"))
				{
					// System.out.println("FORGOTTEN_PRIMEVAL_GARDEN_LOCATIONS.add(new Location(" + activeChar.getX() + ", " + activeChar.getY() + ", " + activeChar.getZ() + "));");
					// activeChar.dropItem("debug loc", activeChar.getInventory().getItemByItemId(57).getObjectId(), 1, activeChar.getX(), activeChar.getY(), activeChar.getZ(), activeChar, false, false);
					// for (int i = 25190225; i < (25190224 + 3); i++)
					{
						activeChar.sendPacket(new TestPacket(activeChar, 25190222));
					}
				}
				else if (s.equals("node"))
				{
					System.out.println("\t\t<node X=\"" + activeChar.getX() + "\" Y=\"" + activeChar.getY() + "\" />");
					activeChar.dropItem("debug node", activeChar.getInventory().getItemByItemId(57).getObjectId(), 1, activeChar.getX(), activeChar.getY(), activeChar.getZ(), activeChar, false, false);
					AdminCommandHandler.getInstance().useAdminCommand(activeChar, "admin_spawn_once Karik 1", false);
				}
				else if (s.equals("requests"))
				{
					if (activeChar.getTarget() == null)
					{
						activeChar.sendPacket(SystemMessageId.INVALID_TARGET);
						return true;
					}
					if (activeChar.getTarget().getActingPlayer() == null)
					{
						activeChar.sendPacket(SystemMessageId.INVALID_TARGET);
						return true;
					}
					activeChar.getTarget().getActingPlayer().removeAllRequests();
				}
				else if (s.equals("interval"))
				{
					if (st.hasMoreTokens())
					{
						String task = st.nextToken();
						if (st.hasMoreTokens())
						{
							int interval = Integer.parseInt(st.nextToken());
							if (task.equals("autofarm"))
							{
								activeChar.sendMessage("Changing Auto Play interval from " + AutoPlayTaskManager.INTERVAL + " to " + interval);
								AutoPlayTaskManager.INTERVAL = interval;
								AutoPlayTaskManager.getInstance().startTasks();
								activeChar.sendMessage("Auto Play interval changed to " + interval);
							}
							else if (task.equals("autouse"))
							{
								activeChar.sendMessage("Changing Auto Use interval from " + AutoUseTaskManager.INTERVAL + " to " + interval);
								AutoUseTaskManager.INTERVAL = interval;
								AutoUseTaskManager.getInstance().startTasks();
								activeChar.sendMessage("Auto Use interval changed to " + interval);
							}
						}
					}
				}
				else if (s.equals("targetspawn"))
				{
					WorldObject target = activeChar.getTarget();
					if (target != null)
					{
						System.out.println("\t\t<npc id=\"" + target.getId() + "\" x=\"" + activeChar.getX() + "\" y=\"" + activeChar.getY() + "\" z=\"" + activeChar.getZ() + "\" heading=\"" + Rnd.get(0, 63999) + "\" respawnTime=\"30sec\" />");
						try
						{
							Spawn spawn = new Spawn(NpcData.getInstance().getTemplate(target.getId()));
							spawn.setXYZ(activeChar.getX() - 10, activeChar.getY() - 10, activeChar.getZ());
							spawn.setHeading(Rnd.get(0, 64000));
							spawn.setAmount(1);
							spawn.setRespawnDelay(0);
							SpawnTable.getInstance().addNewSpawn(spawn, false);
							spawn.init();
							spawn.stopRespawn();
							spawn.getLastSpawn().broadcastInfo();
						}
						catch (ClassNotFoundException | NoSuchMethodException | ClassCastException e)
						{
							e.printStackTrace();
						}
					}
				}
				else if (s.equals("testnpcs"))
				{
					int npcList[] =
					{
						30300,
						30081,
						30936,
						30093,
						31049,
						30847
					};
					for (int i = 0; i < npcList.length; i++)
					{
						try
						{
							final Spawn spawn = new Spawn(NpcData.getInstance().getTemplate(npcList[i]));
							Location loc = activeChar.getLocation();
							spawn.setXYZ(loc.getX() + (i * 50), loc.getY(), loc.getZ());
							spawn.setAmount(1);
							spawn.setHeading(activeChar.getHeading());
							spawn.setRespawnDelay(60);
							SpawnTable.getInstance().addNewSpawn(spawn, false);
							spawn.init();
							spawn.stopRespawn();
							spawn.getLastSpawn().broadcastInfo();
						}
						catch (Exception e)
						{
							activeChar.sendMessage("Couldn't spawn: " + npcList[i]);
						}
					}
				}
				else if (s.equals("toggledebug"))
				{
					if (activeChar.getTarget() == null)
					{
						return false;
					}
					PlayerInstance target = activeChar.getTarget().getActingPlayer();
					if (target == null)
					{
						return false;
					}
					for (Skill skill : target.getAllSkills())
					{
						if (skill.isToggle() && !target.isAffectedBySkill(skill.getId()))
						{
							System.out.println(skill);
						}
					}
				}
				else if (s.equals("level"))
				{
					if (st.hasMoreTokens())
					{
						int newLv = Integer.parseInt(st.nextToken());
						for (PlayerInstance player : World.getInstance().getFakePlayers())
						{
							ThreadPool.get().execute(() ->
							{
								if (player.getLevel() < newLv)
								{
									final long pXp = player.getExp();
									final long tXp = ExperienceData.getInstance().getExpForLevel(newLv);
									if (pXp > tXp)
									{
										player.getStat().setLevel((byte) newLv);
										player.getStat().removeExp(pXp - tXp);
									}
									else if (pXp < tXp)
									{
										player.getStat().addExp(tXp - pXp);
									}
									player.setCurrentHpMp(player.getMaxHp(), player.getMaxMp());
									player.setCurrentCp(player.getMaxCp());
									// player.broadcastUserInfo();
								}
							});
						}
					}
				}
				else if (s.equals("remove_spawns"))
				{
					for (WorldObject obj : World.getInstance().getVisibleObjects())
					{
						if ((obj != null) && obj.isNpc())
						{
							final Npc target = (Npc) obj;
							target.deleteMe();
							final Spawn spawn = target.getSpawn();
							if (spawn != null)
							{
								spawn.stopRespawn();
								SpawnTable.getInstance().deleteSpawn(spawn, false);
							}
						}
					}
				}
				else if (s.equals("remove_shops"))
				{
					for (PlayerInstance player : World.getInstance().getPlayers())
					{
						if (player.isInOfflineMode())
						{
							Disconnection.of(player).logout(false, false);
						}
					}
				}
				else if (s.equals("spawns"))
				{
					for (WorldObject obj : World.getInstance().getVisibleObjects())
					{
						if ((obj != null) && obj.isNpc())
						{
							final Npc target = (Npc) obj;
							target.deleteMe();
							final Spawn spawn = target.getSpawn();
							if (spawn != null)
							{
								spawn.stopRespawn();
								SpawnTable.getInstance().deleteSpawn(spawn, false);
							}
						}
					}
					// Reload.
					SpawnData.getInstance().init();
				}
				else if (s.equals("cw"))
				{
					PlayerInstance player = activeChar.getTarget().getActingPlayer();
					Clan clan1 = player.getClan();
					Clan clan2 = ClanTable.getInstance().getClanByName(st.nextToken());
					clan1.getWarWith(clan2.getId()).cancel(player, clan1);
				}
				else if (s.equals("oly"))
				{
					Olympiad.getInstance().setNewOlympiadEnd();
					Olympiad.getInstance().startOlympiad();
				}
				else if (s.equals("oly2"))
				{
					Olympiad.getInstance().startOlympiad();
				}
				else if (s.equals("oly_stop"))
				{
					Olympiad.getInstance().endOlympiad();
				}
				else if (s.equals("calculate_heroes"))
				{
					Olympiad.getInstance().manualSelectHeroes();
				}
				else if (s.equals("baium"))
				{
					GlobalVariables.BAIUM_RESET_PENDING = true;
				}
				else if (s.equals("packet"))
				{
					String par = st.nextToken();
					if (par.equals("skill"))
					{
						int from = Config.SKILL_BC_RANGE;
						Config.SKILL_BC_RANGE = Integer.parseInt(st.nextToken());
						LOGGER.info("Changed SKILL_BC_RANGE " + from + " -> " + Config.SKILL_BC_RANGE);
					}
					else if (par.equals("common"))
					{
						int from = Config.COMMON_PACKETS_BC_RANGE;
						Config.COMMON_PACKETS_BC_RANGE = Integer.parseInt(st.nextToken());
						LOGGER.info("Changed COMMON_PACKETS_BC_RANGE " + from + " -> " + Config.COMMON_PACKETS_BC_RANGE);
					}
					else if (par.equals("not_critical_broadcasts"))
					{
						int from = Config.NOT_CRITICAL_BROADCASTS_DELAY;
						Config.NOT_CRITICAL_BROADCASTS_DELAY = Integer.parseInt(st.nextToken());
						LOGGER.info("Changed NOT_CRITICAL_BROADCASTS_DELAY " + from + " -> " + Config.NOT_CRITICAL_BROADCASTS_DELAY);
					}
					else if (par.equals("skill_list"))
					{
						int from = Config.SKILL_LIST_DELAY;
						Config.SKILL_LIST_DELAY = Integer.parseInt(st.nextToken());
						LOGGER.info("Changed SKILL_LIST_DELAY " + from + " -> " + Config.SKILL_LIST_DELAY);
					}
					else if (par.equals("client_close"))
					{
						int from = Config.CLIENT_CLOSE_DELAY;
						Config.CLIENT_CLOSE_DELAY = Integer.parseInt(st.nextToken());
						LOGGER.info("Changed CLIENT_CLOSE_DELAY " + from + " -> " + Config.CLIENT_CLOSE_DELAY);
					}
					else if (par.equals("clear_all"))
					{
						for (GameClient client : LoginServerThread.getInstance().getIngameClients())
						{
							client.clearPacketsToWrite();
						}
					}
					// else if (par.equals("resetEstimateSize")) {
					// GameServer.getInstance().getConnectionHandler().debugResetEstimateSize();
					// } else if (par.equals("setEstimateSize")) {
					// GameServer.getInstance().getConnectionHandler().debugSetEstimateSize(Integer.parseInt(st.nextToken()));
					// } else if (par.equals("logInitializations")) {
					// GameServer.getInstance().getConnectionHandler().logInitializations();
					// }
				}
				else if (s.equals("low_performance"))
				{
					boolean par = Boolean.parseBoolean(st.nextToken());
					if (par)
					{
						Config.SKILL_BC_RANGE = 1050;
						Config.COMMON_PACKETS_BC_RANGE = 770;
						Config.NOT_CRITICAL_BROADCASTS_DELAY = 1500;
						Config.CLEAR_PACKETS_TO_WRITE_FROM = 20_000;
						Config.SKILL_LIST_DELAY = 500;
						Config.CLIENT_CLOSE_DELAY = 0;
						Config.NPC_RANDOM_WALKING_ENABLED = false;
						Config.BROADCAST_SHOTS_SELF_ONLY = true;
						Config.BROADCAST_POTIONS_SELF_ONLY = true;
					}
					else
					{
						Config.SKILL_BC_RANGE = 1200;
						Config.COMMON_PACKETS_BC_RANGE = 850;
						Config.NOT_CRITICAL_BROADCASTS_DELAY = 0;
						Config.SKILL_LIST_DELAY = 100;
						Config.CLIENT_CLOSE_DELAY = 2000;
						Config.CLEAR_PACKETS_TO_WRITE_FROM = 100_000;
						Config.NPC_RANDOM_WALKING_ENABLED = true;
						Config.BROADCAST_SHOTS_SELF_ONLY = false;
						Config.BROADCAST_POTIONS_SELF_ONLY = false;
					}
				}
				else if (s.equals("get_disabled_threadpools"))
				{
					final boolean[] disabledPools = ThreadPool.get().getDisabledPools();
					for (int i = 0; i < disabledPools.length; i++)
					{
						System.out.println("POOL " + (i + 1) + " - " + (disabledPools[i] ? "DISABLED" : "ENABLED"));
					}
				}
				else if (s.startsWith("disable_threadpool"))
				{
					final int pool = Integer.parseInt(st.nextToken());
					ThreadPool.get().getDisabledPools()[pool - 1] = true;
					System.out.println("POOL " + pool + " DISABLED");
				}
				else if (s.startsWith("enable_threadpool"))
				{
					final int pool = Integer.parseInt(st.nextToken());
					ThreadPool.get().getDisabledPools()[pool - 1] = false;
					System.out.println("POOL " + pool + " ENABLED");
				}
				else if (s.equals("dtm_on"))
				{
					LOGGER.info("DailyTaskManager on");
					DailyTaskManager.getInstance().enable();
				}
				else if (s.equals("dtm_off"))
				{
					LOGGER.info("DailyTaskManager off");
					DailyTaskManager.getInstance().disable();
				}
				else if (s.equals("merge_adenarip"))
				{
					LOGGER.warning("START");
					long keepAdena = 3_000_000_000L;
					long l8kPrice = 700_000_000L;
					// owner_id, l8kCount
					Map<Integer, Long> adenaMap = new HashMap<>();
					Map<Integer, Long> l8kMap = new HashMap<>();
					try (Connection con = DatabaseFactory.getConnection())
					{
						try (PreparedStatement ps = con.prepareStatement("SELECT owner_id, `count` FROM items WHERE item_id=57 AND `count`>" + (keepAdena + l8kPrice) + " ORDER BY `count` DESC"))
						{
							ResultSet rs = ps.executeQuery();
							while (rs.next())
							{
								final int ownerId = rs.getInt(1);
								final long adenaCount = rs.getLong(2);
								final long l8kcount = (adenaCount - keepAdena) / l8kPrice;
								adenaMap.put(ownerId, adenaCount);
								l8kMap.put(ownerId, l8kcount);
							}
						}
						try (PreparedStatement ps = con.prepareStatement("UPDATE items SET `count`=? WHERE item_id=57 AND owner_id=?"))
						{
							for (Entry<Integer, Long> entry : l8kMap.entrySet())
							{
								ps.setLong(1, adenaMap.get(entry.getKey()) - (entry.getValue() * l8kPrice));
								ps.setInt(2, entry.getKey());
								ps.addBatch();
								System.out.println("Removed " + (entry.getValue() * l8kPrice) + " adena from " + CharNameTable.getInstance().getNameById(entry.getKey()) + " " + (adenaMap.get(entry.getKey()) - (entry.getValue() * l8kPrice)) + " remaining.");
							}
							ps.executeBatch();
						}
						try (PreparedStatement ps = con.prepareStatement("INSERT INTO character_premium_items VALUES (?,?,?,?,?)"))
						{
							for (Entry<Integer, Long> entry : l8kMap.entrySet())
							{
								ps.setInt(1, entry.getKey()); // charId
								ps.setInt(2, 93629); // itemId
								ps.setLong(3, entry.getValue()); // itemCount
								ps.setString(4, "Merge"); // itemSender
								ps.setLong(5, System.currentTimeMillis() + (7 * 86400000L)); // expiryTime
								ps.addBatch();
								System.out.println("Sent " + entry.getValue() + " l8k to " + CharNameTable.getInstance().getNameById(entry.getKey()));
							}
							ps.executeBatch();
						}
					}
					catch (Exception e)
					{
						e.printStackTrace();
					}
					LOGGER.warning("DONE");
				}
				else if (s.equals("merge"))
				{
					Disconnection.of(activeChar).logout(true, true);
					final String DATABASE_URL = "***************************************************************************************";
					final String DATABASE_LOGIN = "root";
					final String DATABASE_PASSWORD = "";
					final String DATABASE_MAX_CONNECTIONS = "100";
					final MariaDbPoolDataSource database2 = new MariaDbPoolDataSource(DATABASE_URL + "&user=" + DATABASE_LOGIN + "&password=" + DATABASE_PASSWORD + "&maxPoolSize=" + DATABASE_MAX_CONNECTIONS);
					// Test if connection is valid.
					try
					{
						database2.getConnection().close();
						LOGGER.info("2 Database: Initialized.");
					}
					catch (Exception e)
					{
						LOGGER.info("2 Database: Problem on initialize. " + e);
					}
					try (Connection con = database2.getConnection(); Statement statement = con.createStatement())
					{
						final long cleanupStart = System.currentTimeMillis();
						int cleanCount = 0;
						// Characters
						cleanCount += statement.executeUpdate("DELETE FROM account_gsdata WHERE account_gsdata.account_name NOT IN (SELECT account_name FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_contacts WHERE character_contacts.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_contacts WHERE character_contacts.contactId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_friends WHERE character_friends.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_friends WHERE character_friends.friendId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_hennas WHERE character_hennas.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_macroses WHERE character_macroses.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_quests WHERE character_quests.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_recipebook WHERE character_recipebook.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_recipeshoplist WHERE character_recipeshoplist.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_shortcuts WHERE character_shortcuts.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_skills WHERE character_skills.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_skills_save WHERE character_skills_save.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_spirits WHERE character_spirits.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_subclasses WHERE character_subclasses.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_surveillances WHERE character_surveillances.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_surveillances WHERE character_surveillances.targetId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_instance_time WHERE character_instance_time.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_daily_rewards WHERE character_daily_rewards.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_henna_potens WHERE character_henna_potens.account_name NOT IN (SELECT account_name FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_item_reuse_save WHERE character_item_reuse_save.charID NOT IN (SELECT charId FROM characters);");
						// Items
						cleanCount += statement.executeUpdate("DELETE FROM items WHERE items.owner_id NOT IN (SELECT charId FROM characters) AND items.owner_id NOT IN (SELECT clan_id FROM clan_data) AND items.owner_id != -1;");
						cleanCount += statement.executeUpdate("DELETE FROM items WHERE items.owner_id = -1 AND loc LIKE 'MAIL' AND loc_data NOT IN (SELECT messageId FROM messages WHERE senderId = -1);");
						cleanCount += statement.executeUpdate("DELETE FROM item_auction_bid WHERE item_auction_bid.playerObjId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM item_variations WHERE item_variations.itemId NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM item_elementals WHERE item_elementals.itemId NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM item_special_abilities WHERE item_special_abilities.objectId NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM item_variables WHERE item_variables.id NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM character_item_reuse_save WHERE character_item_reuse_save.itemObjId NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM character_premium_items WHERE (expiryTime > 0 AND expiryTime < " + System.currentTimeMillis() + ") OR character_premium_items.charId NOT IN (SELECT charId FROM characters)");
						// Misc
						cleanCount += statement.executeUpdate("DELETE FROM cursed_weapons WHERE cursed_weapons.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM heroes WHERE heroes.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM olympiad_nobles WHERE olympiad_nobles.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM olympiad_nobles_prev WHERE olympiad_nobles_prev.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM pets WHERE pets.item_obj_id NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM pet_skills WHERE pet_skills.item_obj_id NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM character_pet_skills_save WHERE character_pet_skills_save.petObjItemId NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM character_summon_skills_save WHERE character_summon_skills_save.ownerId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_summons WHERE character_summons.ownerId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM ranking_statistics WHERE exp_earned = 0 OR ranking_statistics.charId NOT IN (SELECT charId FROM characters) OR time < " + ((int) (System.currentTimeMillis() / 1000) - 1_296_000) + ";");
						cleanCount += statement.executeUpdate("DELETE FROM merchant_lease WHERE merchant_lease.player_id NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_reco_bonus WHERE character_reco_bonus.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_data WHERE clan_data.leader_id NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_data WHERE clan_data.clan_id NOT IN (SELECT clanid FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM olympiad_fights WHERE olympiad_fights.charOneId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM olympiad_fights WHERE olympiad_fights.charTwoId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM heroes_diary WHERE heroes_diary.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_offline_trade WHERE character_offline_trade.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_offline_trade_items WHERE character_offline_trade_items.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_tpbookmark WHERE character_tpbookmark.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_variables WHERE character_variables.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM player_vengeances WHERE player_vengeances.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM random_craft WHERE random_craft.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM steady_box WHERE steady_box.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM toggled_shortcuts WHERE toggled_shortcuts.charId NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM character_shortcuts WHERE character_shortcuts.type=1 AND character_shortcuts.shortcut_id NOT IN (SELECT object_id FROM items);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_dkp_player_data WHERE clan_dkp_player_data.charId NOT IN (SELECT charId from characters);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_dkp_auction_history WHERE clan_dkp_auction_history.charId NOT IN (SELECT charId from characters);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_dkp_shop_history WHERE clan_dkp_shop_history.charId NOT IN (SELECT charId from characters);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_dkp_item_price WHERE clan_dkp_item_price.objectId NOT IN (SELECT object_id from items);");
						// Clan
						cleanCount += statement.executeUpdate("DELETE FROM clan_privs WHERE clan_privs.clan_id NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_skills WHERE clan_skills.clan_id NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_subpledges WHERE clan_subpledges.clan_id NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_wars WHERE clan_wars.clan1 NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_wars WHERE clan_wars.clan2 NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM siege_clans WHERE siege_clans.clan_id NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_notices WHERE clan_notices.clan_id NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM auction_bid WHERE auction_bid.bidderId NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM pledge_waiting_list WHERE pledge_waiting_list.char_id NOT IN (SELECT charId FROM characters);");
						cleanCount += statement.executeUpdate("DELETE FROM clan_variables WHERE clan_variables.clanId NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM pledge_applicant WHERE pledge_applicant.clanId NOT IN (SELECT clan_id FROM clan_data);");
						cleanCount += statement.executeUpdate("DELETE FROM pledge_applicant WHERE pledge_applicant.charId NOT IN (SELECT charId FROM characters);");
						// Forums
						cleanCount += statement.executeUpdate("DELETE FROM forums WHERE forums.forum_owner_id NOT IN (SELECT clan_id FROM clan_data) AND forums.forum_parent=2;");
						cleanCount += statement.executeUpdate("DELETE FROM forums WHERE forums.forum_owner_id NOT IN (SELECT charId FROM characters) AND forums.forum_parent=3;");
						cleanCount += statement.executeUpdate("DELETE FROM posts WHERE posts.post_forum_id NOT IN (SELECT forum_id FROM forums);");
						cleanCount += statement.executeUpdate("DELETE FROM topic WHERE topic.topic_forum_id NOT IN (SELECT forum_id FROM forums);");
						// Update needed items after cleaning has taken place.
						statement.executeUpdate("UPDATE clan_data SET auction_bid_at = 0 WHERE auction_bid_at NOT IN (SELECT auctionId FROM auction_bid);");
						statement.executeUpdate("UPDATE clan_data SET new_leader_id = 0 WHERE new_leader_id <> 0 AND new_leader_id NOT IN (SELECT charId FROM characters);");
						statement.executeUpdate("UPDATE clan_subpledges SET leader_id=0 WHERE clan_subpledges.leader_id NOT IN (SELECT charId FROM characters) AND leader_id > 0;");
						statement.executeUpdate("UPDATE castle SET side='NEUTRAL' WHERE castle.id NOT IN (SELECT hasCastle FROM clan_data);");
						statement.executeUpdate("UPDATE characters SET clanid=0, clan_privs=0, wantspeace=0, subpledge=0, lvl_joined_academy=0, apprentice=0, sponsor=0, clan_join_expiry_time=0, clan_create_expiry_time=0 WHERE characters.clanid > 0 AND characters.clanid NOT IN (SELECT clan_id FROM clan_data);");
						statement.executeUpdate("UPDATE fort SET owner=0 WHERE owner NOT IN (SELECT clan_id FROM clan_data);");
						LOGGER.info("IdManager: Cleaned " + cleanCount + " elements from database in " + ((System.currentTimeMillis() - cleanupStart) / 1000) + " seconds.");
					}
					catch (Exception e)
					{
						LOGGER.warning("IdManager: Could not clean up database: " + e);
					}
					// Merge
					try
					{
						LOGGER.info("DailyTaskManager off");
						DailyTaskManager.getInstance().disable();
						AdditionalSaveTaskManager.ENABLED = false;
						long start = System.currentTimeMillis();
						final Connection connMain = DatabaseFactory.getConnection();
						final Connection connMain_2 = DatabaseFactory.getConnection();
						final Connection conn2 = database2.getConnection();
						int lastObjId = -1;
						ResultSet rsMain;
						ResultSet rs2;
						// @formatter:off
						final String[][] ID_EXTRACTS =
						{
							{
								"characters", "charId"
							},
							{
								"items", "object_id"
							},
							{
								"clan_data", "clan_id"
							},
							// {"itemsonground","object_id"},
							{
								"messages", "messageId"
							}
						};
						// @formatter:on
						// Old 2nd database id, New 1st database id
						final Map<Integer, Integer> replacedObjIds = new HashMap<>();
						final Map<Integer, Integer> replacedCrestIds = new HashMap<>();
						for (String[] temp : ID_EXTRACTS)
						{
							try
							{
								rsMain = connMain.prepareStatement("SELECT " + temp[1] + " FROM " + temp[0] + " ORDER BY " + temp[1] + " DESC LIMIT 1").executeQuery();
								rsMain.next();
								final int val = rsMain.getInt(temp[1]);
								if (val > lastObjId)
								{
									lastObjId = val;
								}
								rs2 = conn2.prepareStatement("SELECT " + temp[1] + " FROM " + temp[0] + " ORDER BY " + temp[1] + " DESC LIMIT 1").executeQuery();
								rs2.next();
								final int val2 = rs2.getInt(temp[1]);
								if (val2 > lastObjId)
								{
									lastObjId = val2;
								}
							}
							catch (Exception e)
							{}
						}
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("Last OID used: " + lastObjId);
						// account_gsdata
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("account_gsdata");
						{
							// PRIME_POINTS
							final Map<String, Integer> mapPrimePoints = new HashMap<>();
							rsMain = connMain.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='PRIME_POINTS'").executeQuery();
							rs2 = conn2.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='PRIME_POINTS'").executeQuery();
							while (rs2.next())
							{
								mapPrimePoints.put(rs2.getString("account_name"), rs2.getInt("value"));
							}
							while (rsMain.next())
							{
								final String accName = rsMain.getString("account_name");
								mapPrimePoints.put(accName, mapPrimePoints.getOrDefault(accName, 0) + rsMain.getInt("value"));
							}
							connMain.prepareStatement("DELETE FROM account_gsdata WHERE var='PRIME_POINTS'").execute();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO account_gsdata (account_name, var, `value`) VALUES (?,?,?)");
							for (Entry<String, Integer> entry : mapPrimePoints.entrySet())
							{
								ps.setString(1, entry.getKey());
								ps.setString(2, "PRIME_POINTS");
								ps.setInt(3, entry.getValue());
								ps.addBatch();
							}
							ps.executeBatch();
							// START_BONUS_PREMIUM
							final List<String> startBonusPremium = new ArrayList<>();
							rsMain = connMain.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='START_BONUS_PREMIUM'").executeQuery();
							rs2 = conn2.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='START_BONUS_PREMIUM'").executeQuery();
							while (rs2.next())
							{
								final String accName = rs2.getString("account_name");
								if (!startBonusPremium.contains(accName))
								{
									startBonusPremium.add(accName);
								}
							}
							while (rsMain.next())
							{
								final String accName = rsMain.getString("account_name");
								if (!startBonusPremium.contains(accName))
								{
									startBonusPremium.add(accName);
								}
							}
							connMain.prepareStatement("DELETE FROM account_gsdata WHERE var='START_BONUS_PREMIUM'").execute();
							ps = connMain.prepareStatement("INSERT INTO account_gsdata (account_name, var, `value`) VALUES (?,?,?)");
							for (String accName : startBonusPremium)
							{
								ps.setString(1, accName);
								ps.setString(2, "START_BONUS_PREMIUM");
								ps.setString(3, "Y");
								ps.addBatch();
							}
							ps.executeBatch();
							// START_BONUS_START
							final List<String> startBonusStart = new ArrayList<>();
							rsMain = connMain.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='START_BONUS_START'").executeQuery();
							rs2 = conn2.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='START_BONUS_START'").executeQuery();
							while (rs2.next())
							{
								final String accName = rs2.getString("account_name");
								if (!startBonusStart.contains(accName))
								{
									startBonusStart.add(accName);
								}
							}
							while (rsMain.next())
							{
								final String accName = rsMain.getString("account_name");
								if (!startBonusStart.contains(accName))
								{
									startBonusStart.add(accName);
								}
							}
							connMain.prepareStatement("DELETE FROM account_gsdata WHERE var='START_BONUS_START'").execute();
							ps = connMain.prepareStatement("INSERT INTO account_gsdata (account_name, var, `value`) VALUES (?,?,?)");
							for (String accName : startBonusStart)
							{
								ps.setString(1, accName);
								ps.setString(2, "START_BONUS_START");
								ps.setString(3, "Y");
								ps.addBatch();
							}
							ps.executeBatch();
							// BONFIRE_RECEIVED
							connMain.prepareStatement("DELETE FROM account_gsdata WHERE var='BONFIRE_RECEIVED'").execute();
							// ATTENDANCE_INDEX
							final Map<String, Integer> attendanceIndex = new HashMap<>();
							rsMain = connMain.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='ATTENDANCE_INDEX'").executeQuery();
							rs2 = conn2.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='ATTENDANCE_INDEX'").executeQuery();
							while (rs2.next())
							{
								final String accName = rs2.getString("account_name");
								attendanceIndex.put(accName, rs2.getInt("value"));
							}
							while (rsMain.next())
							{
								final String accName = rsMain.getString("account_name");
								final int oldValue = attendanceIndex.getOrDefault(accName, -1000);
								final int newValue = rsMain.getInt("value");
								if (oldValue < newValue)
								{
									attendanceIndex.put(accName, newValue);
								}
							}
							connMain.prepareStatement("DELETE FROM account_gsdata WHERE var='ATTENDANCE_INDEX'").execute();
							ps = connMain.prepareStatement("INSERT INTO account_gsdata (account_name, var, `value`) VALUES (?,?,?)");
							for (Entry<String, Integer> entry : attendanceIndex.entrySet())
							{
								ps.setString(1, entry.getKey());
								ps.setString(2, "ATTENDANCE_INDEX");
								ps.setInt(3, entry.getValue());
								ps.addBatch();
							}
							ps.executeBatch();
							// ATTENDANCE_DATE
							final Map<String, Long> attendanceDate = new HashMap<>();
							rsMain = connMain.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='ATTENDANCE_DATE'").executeQuery();
							rs2 = conn2.prepareStatement("SELECT account_name, `value` FROM account_gsdata WHERE var='ATTENDANCE_DATE'").executeQuery();
							while (rs2.next())
							{
								final String accName = rs2.getString("account_name");
								attendanceDate.put(accName, rs2.getLong("value"));
							}
							while (rsMain.next())
							{
								final String accName = rsMain.getString("account_name");
								final long oldValue = attendanceDate.getOrDefault(accName, -1000L);
								final long newValue = rsMain.getLong("value");
								if (oldValue < newValue)
								{
									attendanceDate.put(accName, newValue);
								}
							}
							connMain.prepareStatement("DELETE FROM account_gsdata WHERE var='ATTENDANCE_DATE'").execute();
							ps = connMain.prepareStatement("INSERT INTO account_gsdata (account_name, var, `value`) VALUES (?,?,?)");
							for (Entry<String, Long> entry : attendanceDate.entrySet())
							{
								ps.setString(1, entry.getKey());
								ps.setString(2, "ATTENDANCE_DATE");
								ps.setLong(3, entry.getValue());
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// account_premium
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("account_premium");
						{
							final long currentMillis = System.currentTimeMillis();
							final Map<String, Long> accountPremium = new HashMap<>();
							rsMain = connMain.prepareStatement("SELECT account_name, enddate FROM account_premium").executeQuery();
							rs2 = conn2.prepareStatement("SELECT account_name, enddate FROM account_premium").executeQuery();
							while (rsMain.next())
							{
								long remaining = rsMain.getLong("enddate") - currentMillis;
								if (remaining > 0)
								{
									accountPremium.put(rsMain.getString("account_name"), remaining);
								}
							}
							while (rs2.next())
							{
								long remaining = rs2.getLong("enddate") - currentMillis;
								if (remaining > 0)
								{
									accountPremium.put(rs2.getString("account_name"), accountPremium.getOrDefault(rs2.getString("account_name"), 0L) + remaining);
								}
							}
							connMain.prepareStatement("DELETE FROM account_premium").execute();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO account_premium (account_name, enddate) VALUES (?,?)");
							for (Entry<String, Long> entry : accountPremium.entrySet())
							{
								if (entry.getValue() <= 0)
								{
									continue;
								}
								ps.setString(1, entry.getKey());
								ps.setLong(2, entry.getValue() + System.currentTimeMillis());
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// account_random_craft_premium
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("account_random_craft_premium");
						{
							final long currentMillis = System.currentTimeMillis();
							final Map<String, Long> accountPremium = new HashMap<>();
							rsMain = connMain.prepareStatement("SELECT account_name, enddate FROM account_random_craft_premium").executeQuery();
							rs2 = conn2.prepareStatement("SELECT account_name, enddate FROM account_random_craft_premium").executeQuery();
							while (rsMain.next())
							{
								long remaining = rsMain.getLong("enddate") - currentMillis;
								if (remaining > 0)
								{
									accountPremium.put(rsMain.getString("account_name"), remaining);
								}
							}
							while (rs2.next())
							{
								long remaining = rs2.getLong("enddate") - currentMillis;
								if (remaining > 0)
								{
									accountPremium.put(rs2.getString("account_name"), accountPremium.getOrDefault(rs2.getString("account_name"), 0L) + remaining);
								}
							}
							connMain.prepareStatement("DELETE FROM account_random_craft_premium").execute();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO account_random_craft_premium (account_name, enddate) VALUES (?,?)");
							for (Entry<String, Long> entry : accountPremium.entrySet())
							{
								if (entry.getValue() <= 0)
								{
									continue;
								}
								ps.setString(1, entry.getKey());
								ps.setLong(2, entry.getValue() + System.currentTimeMillis());
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// castle
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("castle");
						connMain.prepareStatement("UPDATE castle SET treasury=0, side='NEUTRAL'").execute();
						// castle_doorupgrade
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("castle_doorupgrade");
						connMain.prepareStatement("DELETE FROM castle_doorupgrade").execute();
						// castle_functions
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("castle_functions");
						connMain.prepareStatement("DELETE FROM castle_functions").execute();
						// characters
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("characters");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM characters").executeQuery();
							final ResultSetMetaData meta2 = rs2.getMetaData();
							final int columnCount2 = meta2.getColumnCount();
							rsMain = connMain.prepareStatement("SELECT char_name, exp, charId FROM characters").executeQuery();
							PreparedStatement psMain_2 = connMain_2.prepareStatement("UPDATE characters SET char_name=? WHERE charId=?");
							boolean needRenameQuery = false;
							final Map<String, Long> expMapMain = new HashMap<>();
							final Map<String, Integer> charIdMapMain = new HashMap<>();
							while (rsMain.next())
							{
								expMapMain.put(rsMain.getString(1).toLowerCase(), rsMain.getLong(2));
								charIdMapMain.put(rsMain.getString(1).toLowerCase(), rsMain.getInt(3));
							}
							String temp = "?";
							for (int i = 1; i < columnCount2; i++)
							{
								temp += ",?";
							}
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO characters VALUES (" + temp + ")");
							while (rs2.next())
							{
								ps.clearParameters();
								String charName = null;
								int charNameColumnIndex = -1;
								for (int i = 1; i <= columnCount2; ++i)
								{
									final String columnName = meta2.getColumnName(i);
									String value = rs2.getString(i);
									if (columnName.equals("charId"))
									{
										final int oldObjId = rs2.getInt(i);
										lastObjId++;
										replacedObjIds.put(oldObjId, lastObjId);
										value = String.valueOf(lastObjId);
									}
									else if (columnName.equals("char_name"))
									{
										charName = rs2.getString(i);
										charNameColumnIndex = i;
										continue;
									}
									else if (columnName.equals("clanid"))
									{
										final int oldObjId = rs2.getInt(i);
										final Integer replacedObjId = replacedObjIds.get(oldObjId);
										if (replacedObjId == null)
										{
											lastObjId++;
											replacedObjIds.put(oldObjId, lastObjId);
											value = String.valueOf(lastObjId);
										}
										else
										{
											value = String.valueOf(replacedObjId.intValue());
										}
									}
									else if (columnName.equals("exp"))
									{
										if ((charName == null) || (charNameColumnIndex < 1))
										{
											LOGGER.warning("null charName - should be impossible");
											break;
										}
										if (charIdMapMain.keySet().contains(charName.toLowerCase()))
										{
											final long exp2 = rs2.getLong(i);
											final long expMain = expMapMain.getOrDefault(charName.toLowerCase(), -1L);
											if (exp2 <= expMain) // 1st Server has more exp than 1st Server
											{
												charName = "02_" + charName;
												if (charName.length() > 16)
												{
													charName = charName.substring(0, charName.length() - (charName.length() - 16));
												}
											}
											else // 2nd Server has more exp than 2nd server
											{
												String tempName = "02_" + charName;
												if (tempName.length() > 16)
												{
													tempName = tempName.substring(0, tempName.length() - (tempName.length() - 16));
												}
												final int id = charIdMapMain.get(charName.toLowerCase());
												charIdMapMain.put(tempName.toLowerCase(), id);
												psMain_2.clearParameters();
												psMain_2.setString(1, tempName);
												psMain_2.setInt(2, id);
												psMain_2.addBatch();
												needRenameQuery = true;
											}
										}
										ps.setString(charNameColumnIndex, charName);
									}
									ps.setString(i, value);
								}
								ps.addBatch();
							}
							if (needRenameQuery)
							{
								psMain_2.executeBatch();
							}
							ps.executeBatch();
						}
						// character_contacts
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_contacts");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_contacts").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_contacts VALUES (?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // contactId
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_daily_rewards
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_daily_rewards");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_daily_rewards").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_daily_rewards VALUES (?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // rewardId
								ps.setInt(3, rs2.getInt(3)); // status
								ps.setInt(4, rs2.getInt(4)); // progress
								ps.setLong(5, rs2.getLong(5)); // lastCompleted
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_friends
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_friends");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_friends").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_friends VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // friendId
								ps.setInt(3, rs2.getInt(3)); // relation
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_henna_potens
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_henna_potens");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_henna_potens").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_henna_potens VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								for (int i = 2; i <= 13; i++)
								{
									ps.setInt(i, rs2.getInt(i)); // data
								}
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_hennas
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_hennas");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_hennas").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_hennas VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								for (int i = 2; i <= 4; i++)
								{
									ps.setInt(i, rs2.getInt(i)); // data
								}
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_instance_time
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_instance_time");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_instance_time").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_instance_time VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2));
								ps.setLong(3, rs2.getLong(3));
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_data
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_data");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM clan_data").executeQuery();
							rsMain = connMain.prepareStatement("SELECT clan_name, clan_exp, clan_id FROM clan_data").executeQuery();
							PreparedStatement psMain_2 = connMain_2.prepareStatement("UPDATE clan_data SET clan_name=? WHERE clan_id=?");
							boolean needRenameQuery = false;
							final Map<String, Double> expMapMain = new HashMap<>();
							final Map<String, Integer> clanIdMapMain = new HashMap<>();
							while (rsMain.next())
							{
								expMapMain.put(rsMain.getString(1).toLowerCase(), rsMain.getDouble(2));
								clanIdMapMain.put(rsMain.getString(1).toLowerCase(), rsMain.getInt(3));
							}
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_data VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								int clanId = replacedObjIds.getOrDefault(rs2.getInt(1), -1);
								if (clanId < 0)
								{
									lastObjId++;
									clanId = lastObjId;
									replacedObjIds.put(rs2.getInt(1), clanId);
								}
								ps.setInt(1, clanId); // clan_id
								String clanName = rs2.getString(2);
								final double exp2 = rs2.getDouble(3);
								if (clanIdMapMain.keySet().contains(clanName.toLowerCase()))
								{
									final double expMain = expMapMain.getOrDefault(clanName.toLowerCase(), -1d);
									if (exp2 <= expMain) // 1st Server has more exp than 1st Server
									{
										clanName = "02_" + clanName;
										if (clanName.length() > 16)
										{
											clanName = clanName.substring(0, clanName.length() - (clanName.length() - 16));
										}
									}
									else // 2nd Server has more exp than 2nd server
									{
										String tempName = "02_" + clanName;
										if (tempName.length() > 16)
										{
											tempName = tempName.substring(0, tempName.length() - (tempName.length() - 16));
										}
										final int id = clanIdMapMain.get(clanName.toLowerCase());
										clanIdMapMain.put(tempName.toLowerCase(), id);
										psMain_2.clearParameters();
										psMain_2.setString(1, tempName);
										psMain_2.setInt(2, id);
										psMain_2.addBatch();
										needRenameQuery = true;
									}
								}
								ps.setString(2, clanName); // clan_name
								ps.setDouble(3, exp2); // clan_exp
								ps.setInt(4, rs2.getInt(4)); // clan_level
								ps.setInt(5, 0); // hasCastle
								ps.setInt(6, rs2.getInt(6)); // blood_alliance_count
								ps.setInt(7, rs2.getInt(7)); // blood_oath_count
								ps.setInt(8, 0); // ally_id
								ps.setString(9, null); // ally_name
								ps.setInt(10, replacedObjIds.get(rs2.getInt(10))); // leader_id
								final int oldCrestId = rs2.getInt(11);
								if (oldCrestId > 0)
								{
									int crestId = replacedCrestIds.getOrDefault(oldCrestId, -1);
									if (crestId < 0)
									{
										crestId = CrestTable.getInstance().getNextId();
										replacedObjIds.put(oldCrestId, crestId);
									}
									ps.setInt(11, crestId);// crest_id
								}
								else
								{
									ps.setInt(11, 0);// crest_id
								}
								final int oldCrestLargeId = rs2.getInt(12);
								if (oldCrestLargeId > 0)
								{
									int crestLargeId = replacedCrestIds.getOrDefault(oldCrestLargeId, -1);
									if (crestLargeId < 0)
									{
										crestLargeId = CrestTable.getInstance().getNextId();
										replacedObjIds.put(oldCrestLargeId, crestLargeId);
									}
									ps.setInt(12, crestLargeId);// crest_large_id
								}
								else
								{
									ps.setInt(12, 0);// crest_large_id
								}
								ps.setInt(13, 0);// ally_crest_id
								ps.setInt(14, 0); // auction_bid_at
								ps.setLong(15, 0); // ally_penalty_expiry_time
								ps.setLong(16, 0); // ally_penalty_type
								ps.setLong(17, 0); // char_penalty_expiry_time
								ps.setLong(18, 0); // dissolving_expiry_time
								ps.setInt(19, 0); // new_leader_id
								ps.addBatch();
							}
							if (needRenameQuery)
							{
								psMain_2.executeBatch();
							}
							ps.executeBatch();
						}
						// messages
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("messages");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM messages").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO messages VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								final int oldObjId = rs2.getInt(1);
								lastObjId++;
								replacedObjIds.put(oldObjId, lastObjId);
								ps.setInt(1, lastObjId); // messageId
								if (rs2.getInt(2) > 0)
								{
									if (replacedObjIds.getOrDefault(rs2.getInt(2), -1) < 0)
									{
										ps.setInt(2, -1);
									}
									else
									{
										ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // senderId
									}
								}
								else
								{
									ps.setInt(2, rs2.getInt(2)); // senderId
								}
								if (rs2.getInt(3) > 0)
								{
									if (replacedObjIds.getOrDefault(rs2.getInt(3), -1) < 0)
									{
										continue;
									}
									ps.setInt(3, replacedObjIds.get(rs2.getInt(3))); // receiverId
								}
								else
								{
									ps.setInt(3, rs2.getInt(3)); // receiverId
								}
								ps.setString(4, rs2.getString(4)); // subject
								ps.setString(5, rs2.getString(5)); // content
								ps.setLong(6, rs2.getLong(6)); // expiration
								ps.setLong(7, rs2.getLong(7)); // reqAdena
								ps.setString(8, rs2.getString(8)); // hasAttachments
								ps.setString(9, rs2.getString(9)); // isUnread
								ps.setString(10, rs2.getString(10)); // isDeletedBySender
								ps.setString(11, rs2.getString(11)); // isDeletedByReceiver
								ps.setString(12, rs2.getString(12)); // isLocked
								ps.setInt(13, rs2.getInt(13)); // sendBySystem
								ps.setString(14, rs2.getString(14)); // isReturned
								ps.setInt(15, rs2.getInt(15)); // itemId
								ps.setInt(16, rs2.getInt(16)); // enchantLvl
								ps.setString(17, rs2.getString(17)); // elementals
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// items
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("items");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM items").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO items VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								String loc = rs2.getString(7);
								final int oldOwnerId = rs2.getInt(1);
								if (oldOwnerId < 0)
								{
									if (loc.equals("MAIL"))
									{
										ps.setInt(1, oldOwnerId); // owner_id
									}
									else
									{
										continue;
									}
								}
								else
								{
									final int newOwnerId = replacedObjIds.getOrDefault(oldOwnerId, -1);
									if (newOwnerId < 0)
									{
										continue;
									}
									ps.setInt(1, newOwnerId); // owner_id
								}
								final int oldObjId = rs2.getInt(2);
								lastObjId++;
								replacedObjIds.put(oldObjId, lastObjId);
								ps.setInt(2, lastObjId); // object_id
								ps.setInt(3, rs2.getInt(3)); // item_id
								ps.setLong(4, rs2.getLong(4)); // count
								ps.setInt(5, rs2.getInt(5)); // enchant_level
								ps.setInt(6, rs2.getInt(6)); // is_blessed
								if (loc.equals("PET_EQUIP"))
								{
									loc = "INVENTORY";
								}
								ps.setString(7, loc); // loc
								if (loc.equals("MAIL"))
								{
									if (replacedObjIds.getOrDefault(rs2.getInt(8), -1) < 0)
									{
										continue;
									}
									ps.setInt(8, replacedObjIds.get(rs2.getInt(8))); // loc_data;
								}
								else
								{
									ps.setInt(8, rs2.getInt(8)); // loc_data;
								}
								ps.setString(9, rs2.getString(9)); // time_of_use
								ps.setInt(10, rs2.getInt(10)); // custom_type1
								ps.setInt(11, rs2.getInt(11)); // custom_type2
								ps.setInt(12, rs2.getInt(12)); // mana_left
								ps.setLong(13, rs2.getLong(13)); // time
								ps.setInt(14, 0); // pet_id
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_item_reuse_save
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_item_reuse_save");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_item_reuse_save").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_item_reuse_save VALUES (?,?,?,?,?)");
							while (rs2.next())
							{
								final int newCharId = replacedObjIds.getOrDefault(rs2.getInt(1), -1);
								if (newCharId < 0)
								{
									continue;
								}
								ps.setInt(1, newCharId); // charId
								ps.setInt(2, rs2.getInt(2)); // itemId
								final int newItemObjId = replacedObjIds.getOrDefault(rs2.getInt(3), -1);
								if (newItemObjId < 0)
								{
									continue;
								}
								ps.setInt(3, newItemObjId); // itemObjId
								ps.setLong(4, rs2.getLong(4)); // reuseDelay
								ps.setLong(5, rs2.getLong(5)); // sysTime
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_macroses
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_macroses");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_macroses").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_macroses VALUES (?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // id
								ps.setInt(3, rs2.getInt(3)); // icon
								ps.setString(4, rs2.getString(4)); // name
								ps.setString(5, rs2.getString(5)); // descr
								ps.setString(6, rs2.getString(6)); // acronym
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_macroses_commands
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_macroses_commands");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_macroses_commands").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_macroses_commands VALUES (?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // id
								ps.setInt(3, rs2.getInt(3)); // command_index
								ps.setInt(4, rs2.getInt(4)); // type
								ps.setInt(5, rs2.getInt(5)); // d1
								ps.setInt(6, rs2.getInt(6)); // d2
								ps.setString(7, rs2.getString(7)); // command
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_pet_skills_save
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_pet_skills_save");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_pet_skills_save").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_pet_skills_save VALUES (?,?,?,?,?,?)");
							while (rs2.next())
							{
								final int newObjId = replacedObjIds.getOrDefault(rs2.getInt(1), -1);
								if (newObjId < 0)
								{
									continue;
								}
								ps.setInt(1, newObjId); // petObjItemId
								ps.setInt(2, rs2.getInt(2)); // skill_id
								ps.setInt(3, rs2.getInt(3)); // skill_level
								ps.setInt(4, rs2.getInt(4)); // skill_sub_level
								ps.setLong(5, rs2.getLong(5)); // remaining_time
								ps.setInt(6, rs2.getInt(6)); // buff_index
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_premium_items
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_premium_items");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_premium_items").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_premium_items VALUES (?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // itemId
								ps.setLong(3, rs2.getLong(3)); // itemCount
								ps.setString(4, rs2.getString(4)); // itemSender
								ps.setLong(5, rs2.getLong(5)); // expiryTime
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_quests
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_quests");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_quests").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_quests VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setString(2, rs2.getString(2)); // name
								ps.setString(3, rs2.getString(3)); // var
								ps.setString(4, rs2.getString(4)); // value
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_recipebook
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_recipebook");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_recipebook").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_recipebook VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // id
								ps.setInt(3, rs2.getInt(3)); // classIndex
								ps.setInt(4, rs2.getInt(4)); // type
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_recipeshoplist
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_recipeshoplist");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_recipeshoplist").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_recipeshoplist VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // recipeId
								ps.setLong(3, rs2.getLong(3)); // price
								ps.setInt(4, rs2.getInt(4)); // index
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_reco_bonus
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_reco_bonus");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_reco_bonus").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_reco_bonus VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // rec_have
								ps.setInt(3, rs2.getInt(3)); // rec_left
								ps.setLong(4, rs2.getLong(4)); // time_left
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_shortcuts
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_shortcuts");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_shortcuts").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_shortcuts VALUES (?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // slot
								ps.setInt(3, rs2.getInt(3)); // page
								final int type = rs2.getInt(4);
								ps.setInt(4, type);
								switch (type)
								{
									case 1: // Item
									{
										final int newObjId = replacedObjIds.getOrDefault(rs2.getInt(5), -1);
										if (newObjId < 0)
										{
											continue;
										}
										ps.setInt(5, newObjId);
										break;
									}
									case 2: // Skill
									case 3: // Action
									case 4: // Macro
									case 6: // Bookmark
									default: // All
									{
										ps.setInt(5, rs2.getInt(5));
										break;
									}
								}
								ps.setInt(6, rs2.getInt(6)); // level
								ps.setInt(7, rs2.getInt(7)); // sub_level
								ps.setInt(8, rs2.getInt(8)); // class_index
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_skills
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_skills");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_skills").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_skills VALUES (?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // skill_id
								ps.setInt(3, rs2.getInt(3)); // skill_level
								ps.setInt(4, rs2.getInt(4)); // skill_sub_level
								ps.setInt(5, rs2.getInt(5)); // class_index
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_skills_save
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_skills_save");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_skills_save").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_skills_save VALUES (?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // skill_id
								ps.setInt(3, rs2.getInt(3)); // skill_level
								ps.setInt(4, rs2.getInt(4)); // skill_sub_level
								ps.setLong(5, rs2.getLong(5)); // remaining_time
								ps.setLong(6, rs2.getLong(6)); // reuse_delay
								ps.setLong(7, rs2.getLong(7)); // systime
								ps.setInt(8, rs2.getInt(8)); // restore_type
								ps.setInt(9, rs2.getInt(9)); // class_index
								ps.setInt(10, rs2.getInt(10)); // buff_index
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_spirits
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_spirits");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_spirits").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_spirits VALUES (?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // type
								ps.setInt(3, rs2.getInt(3)); // level
								ps.setInt(4, rs2.getInt(4)); // stage
								ps.setLong(5, rs2.getLong(5)); // experience
								ps.setInt(6, rs2.getInt(6)); // attack_points
								ps.setInt(7, rs2.getInt(7)); // defense_points
								ps.setInt(8, rs2.getInt(8)); // crit_rate_points
								ps.setInt(9, rs2.getInt(9)); // crit_damage_points
								ps.setBoolean(10, rs2.getBoolean(10)); // in_use
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_summon_skills_save
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_summon_skills_save");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_summon_skills_save").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_summon_skills_save VALUES (?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // ownerId
								ps.setInt(2, rs2.getInt(2)); // ownerClassIndex
								ps.setInt(3, rs2.getInt(3)); // summonSkillId
								ps.setInt(4, rs2.getInt(4)); // skill_id
								ps.setInt(5, rs2.getInt(5)); // skill_level
								ps.setInt(6, rs2.getInt(6)); // skill_sub_level
								ps.setLong(7, rs2.getLong(7)); // remaining_time
								ps.setInt(8, rs2.getInt(8)); // buff_index
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_summons
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_summons");
						connMain.prepareStatement("DELETE FROM character_summons").execute();
						// character_surveillances
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_surveillances");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_surveillances").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_surveillances VALUES (?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // targetId
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_tpbookmark
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_tpbookmark");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM character_tpbookmark").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_tpbookmark VALUES (?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // id
								ps.setInt(3, rs2.getInt(3)); // x
								ps.setInt(4, rs2.getInt(4)); // y
								ps.setInt(5, rs2.getInt(5)); // z
								ps.setInt(6, rs2.getInt(6)); // icon
								ps.setString(7, rs2.getString(7)); // tag
								ps.setString(8, rs2.getString(8)); // name
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_auction_history
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_auction_history");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_auction_history WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_auction_history").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_auction_history VALUES (?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // charId
								ps.setInt(3, rs2.getInt(3)); // itemId
								ps.setLong(4, rs2.getLong(4)); // itemCount
								ps.setLong(5, rs2.getLong(5)); // price
								ps.setBoolean(6, rs2.getBoolean(6)); // enchantLevel
								ps.setInt(7, rs2.getInt(7)); // isAugmented
								ps.setLong(8, rs2.getLong(8)); // timestamp
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_data
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_data");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_data WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_data").executeQuery();
							final ResultSetMetaData meta2 = rs2.getMetaData();
							final int columnCount2 = meta2.getColumnCount();
							String temp = "?";
							for (int i = 1; i < columnCount2; i++)
							{
								temp += ",?";
							}
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_data VALUES (" + temp + ")");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								for (int i = 2; i <= columnCount2; i++)
								{
									ps.setInt(i, rs2.getInt(i)); // data
								}
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_dynamic_stars_chars
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_dynamic_stars_chars");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_chars WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_dynamic_stars_chars").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_dynamic_stars_chars VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // charId
								ps.setInt(3, rs2.getInt(3)); // value
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_dynamic_stars_classes
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_dynamic_stars_classes");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_classes WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_dynamic_stars_classes").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_dynamic_stars_classes VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, rs2.getInt(2)); // classId
								ps.setInt(3, rs2.getInt(3)); // value
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_dynamic_stars_items
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_dynamic_stars_items");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_items WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_dynamic_stars_items").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_dynamic_stars_items VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, rs2.getInt(2)); // enumId
								ps.setInt(3, rs2.getInt(3)); // value
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_dynamic_stars_levels
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_dynamic_stars_levels");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_levels WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_dynamic_stars_levels").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_dynamic_stars_levels VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, rs2.getInt(2)); // level
								ps.setInt(3, rs2.getInt(3)); // value
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_dynamic_stars_skills
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_dynamic_stars_skills");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_dynamic_stars_skills WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_dynamic_stars_skills").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_dynamic_stars_skills VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, rs2.getInt(2)); // skillId
								ps.setInt(3, rs2.getInt(3)); // value
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_item_price
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_item_price");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_item_price WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_item_price").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_item_price VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								final int newObjId = replacedObjIds.getOrDefault(rs2.getInt(2), -1);
								if (newObjId < 0)
								{
									continue;
								}
								ps.setInt(2, newObjId); // objectId
								ps.setInt(3, rs2.getInt(3)); // price
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_player_data
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_player_data");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_player_data WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_player_data").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_player_data VALUES (?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // charId
								ps.setInt(3, rs2.getInt(3)); // stars
								ps.setInt(4, rs2.getInt(4)); // dynamicStars
								ps.setInt(5, rs2.getInt(5)); // points
								ps.setInt(6, rs2.getInt(6)); // activity
								ps.setBoolean(7, rs2.getBoolean(7)); // hasLeaderRights
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_player_history
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_player_history");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_player_history WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_player_history").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_player_history VALUES (?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // charId
								ps.setInt(3, rs2.getInt(3)); // referenceId
								ps.setInt(4, rs2.getInt(4)); // type
								ps.setInt(5, rs2.getInt(5)); // eventId
								ps.setInt(6, rs2.getInt(6)); // change
								ps.setLong(7, rs2.getLong(7)); // timestamp
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_dkp_shop_history
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_dkp_shop_history");
						{
							conn2.prepareStatement("DELETE FROM clan_dkp_shop_history WHERE clanId NOT IN (SELECT clan_id from clan_data)").execute();
							rs2 = conn2.prepareStatement("SELECT * FROM clan_dkp_shop_history").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_dkp_shop_history VALUES (?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								final int newObjId = replacedObjIds.getOrDefault(rs2.getInt(2), -1);
								if (newObjId < 0)
								{
									continue;
								}
								ps.setInt(2, newObjId); // charId
								ps.setInt(3, rs2.getInt(3)); // itemId
								ps.setLong(4, rs2.getLong(4)); // itemCount
								ps.setLong(5, rs2.getLong(5)); // price
								ps.setBoolean(6, rs2.getBoolean(6)); // enchantLevel
								ps.setInt(7, rs2.getInt(7)); // isAugmented
								ps.setLong(8, rs2.getLong(8)); // timestamp
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_notices
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_notices");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM clan_notices").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_notices VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setString(2, rs2.getString(2)); // enabled
								ps.setString(3, rs2.getString(3)); // notice
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_privs
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_privs");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM clan_privs").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_privs VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, rs2.getInt(2)); // rank
								ps.setInt(3, rs2.getInt(3)); // party
								ps.setInt(4, rs2.getInt(4)); // privs
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_skills
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_skills");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM clan_skills").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_skills VALUES (?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clanId
								ps.setInt(2, rs2.getInt(2)); // skill_id
								ps.setInt(3, rs2.getInt(3)); // skill_level
								ps.setString(4, rs2.getString(4)); // skill_name
								ps.setInt(5, rs2.getInt(5)); // sub_pledge_id
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_variables
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_variables");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM clan_variables").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_variables VALUES (?,?,?)");
							while (rs2.next())
							{
								final int newObjId = replacedObjIds.getOrDefault(rs2.getInt(1), -1);
								if (newObjId < 0)
								{
									continue;
								}
								ps.setInt(1, newObjId); // clanId
								ps.setString(2, rs2.getString(2)); // var
								ps.setString(3, rs2.getString(3)); // val
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// clan_wars
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("clan_wars");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM clan_wars").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO clan_wars VALUES (?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // clan1
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // clan2
								ps.setInt(3, rs2.getInt(3)); // clan1Kill
								ps.setInt(4, rs2.getInt(4)); // clan2Kill
								int winnerClan = rs2.getInt(5);
								if (winnerClan > 0)
								{
									winnerClan = replacedObjIds.get(winnerClan);
								}
								ps.setInt(5, winnerClan); // winnerClan
								ps.setLong(6, rs2.getLong(6)); // startTime
								ps.setLong(7, rs2.getLong(7)); // endTime
								ps.setInt(8, rs2.getInt(8)); // state
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// collection_favorites
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("collection_favorites");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM collection_favorites").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("REPLACE INTO collection_favorites VALUES (?,?)");
							while (rs2.next())
							{
								ps.setString(1, rs2.getString(1)); // accountName
								ps.setInt(2, rs2.getInt(2)); // collectionId
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// collections
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("collections");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM collections").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("REPLACE INTO collections VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setString(1, rs2.getString(1)); // accountName
								ps.setInt(2, rs2.getInt(2)); // itemId
								ps.setInt(3, rs2.getInt(3)); // collectionId
								ps.setInt(4, rs2.getInt(4)); // index
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// crests
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("crests");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM crests").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO crests VALUES (?,?,?)");
							while (rs2.next())
							{
								int crestId = replacedCrestIds.getOrDefault(rs2.getInt(1), -1);
								if (crestId < 0)
								{
									continue;
								}
								ps.setInt(1, crestId); // crest_id
								ps.setBytes(2, rs2.getBytes(2)); // data
								ps.setInt(3, rs2.getInt(3)); // type
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// fort
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("fort");
						connMain.prepareStatement("UPDATE fort SET lastOwnedTime=0, owner=0");
						// global_variables
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("global_variables");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM global_variables").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO global_variables VALUES (?,?)");
							while (rs2.next())
							{
								String var = rs2.getString(1);
								String val = rs2.getString(2);
								if (var.startsWith("MA_C"))
								{
									final int newObjId = replacedObjIds.getOrDefault(Integer.parseInt(var.replace("MA_C", "")), -1);
									if (newObjId < 0)
									{
										continue;
									}
									var = "MA_C" + newObjId;
								}
								else if (var.startsWith("dkp_") && Boolean.parseBoolean(val))
								{
									final int newObjId = replacedObjIds.getOrDefault(Integer.parseInt(var.replace("dkp_", "")), -1);
									if (newObjId < 0)
									{
										continue;
									}
									var = "dkp_" + newObjId;
								}
								else
								{
									continue;
								}
								ps.setString(1, var); // var
								ps.setString(2, val); // val
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// heroes
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("heroes");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM heroes").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO heroes VALUES (?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // class_id
								ps.setInt(3, rs2.getInt(3)); // count
								ps.setInt(4, rs2.getInt(4)); // legend_count
								ps.setInt(5, rs2.getInt(5)); // played
								ps.setString(6, rs2.getString(6)); // claimed
								ps.setString(7, rs2.getString(7)); // message
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// heroes_diary
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("heroes_diary");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM heroes_diary").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO heroes_diary VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setLong(2, rs2.getLong(2)); // time
								ps.setInt(3, rs2.getInt(3)); // action
								ps.setInt(4, rs2.getInt(4)); // param
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// item_special_abilities
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("item_special_abilities");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM item_special_abilities").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO item_special_abilities VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // objectId
								ps.setInt(2, rs2.getInt(2)); // type
								ps.setInt(3, rs2.getInt(3)); // optionId
								ps.setInt(4, rs2.getInt(4)); // position
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// item_variables
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("item_variables");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM item_variables").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO item_variables VALUES (?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // id
								ps.setString(2, rs2.getString(2)); // var
								ps.setString(3, rs2.getString(3)); // val
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// item_variations
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("item_variations");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM item_variations").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO item_variations VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // itemId
								ps.setInt(2, rs2.getInt(2)); // mineralId
								ps.setInt(3, rs2.getInt(3)); // option1
								ps.setInt(4, rs2.getInt(4)); // option2
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// l2pass
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("l2pass");
						{
							List<String> accountsMain = new ArrayList<>();
							rs2 = conn2.prepareStatement("SELECT * FROM l2pass").executeQuery();
							rsMain = connMain.prepareStatement("SELECT account_name FROM l2pass").executeQuery();
							while (rsMain.next())
							{
								accountsMain.add(rsMain.getString("account_name"));
							}
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO l2pass VALUES (?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								if (!accountsMain.contains(rs2.getString("account_name")))
								{
									ps.setString(1, rs2.getString(1)); // account_name
									ps.setInt(2, rs2.getInt(2)); // points
									ps.setInt(3, rs2.getInt(3)); // premium
									ps.setInt(4, rs2.getInt(4)); // sayha_sustention_time_earned
									ps.setInt(5, rs2.getInt(5)); // sayha_sustention_time_used
									ps.setInt(6, rs2.getInt(6)); // reward_step
									ps.setInt(7, rs2.getInt(7)); // premium_reward_step
									ps.addBatch();
								}
							}
							ps.executeBatch();
						}
						// lcoin_shop_limits_used
						connMain.prepareStatement("DELETE FROM lcoin_shop_limits_used").execute();
						// olympiad_fights
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("olympiad_fights");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM olympiad_fights").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO olympiad_fights VALUES (?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charOneId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // charTwoId
								ps.setInt(3, rs2.getInt(3)); // charOneClass
								ps.setInt(4, rs2.getInt(4)); // charTwoClass
								ps.setInt(5, rs2.getInt(5)); // winner
								ps.setLong(6, rs2.getLong(6)); // start
								ps.setLong(7, rs2.getLong(7)); // time
								ps.setInt(8, rs2.getInt(8)); // classed
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// olympiad_nobles
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("olympiad_nobles");
						connMain.prepareStatement("DELETE FROM olympiad_nobles").execute();
						// olympiad_nobles_prev
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("olympiad_nobles_prev");
						connMain.prepareStatement("DELETE FROM olympiad_nobles_prev").execute();
						// pet_skills
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("pet_skills");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM pet_skills").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO pet_skills VALUES (?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // item_obj_id
								ps.setInt(2, rs2.getInt(2)); // skill_id
								ps.setInt(3, rs2.getInt(3)); // skill_level
								ps.setInt(4, rs2.getInt(4)); // skill_sub_level
								ps.setInt(5, rs2.getInt(5)); // removed
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// pets
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("pets");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM pets").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO pets VALUES (?,?,?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // item_obj_id
								ps.setString(2, rs2.getString(2)); // name
								ps.setInt(3, rs2.getInt(3)); // level
								ps.setInt(4, rs2.getInt(4)); // curHp
								ps.setInt(5, rs2.getInt(5)); // curMp
								ps.setLong(6, rs2.getLong(6)); // exp
								ps.setLong(7, rs2.getLong(7)); // sp
								ps.setLong(8, rs2.getLong(8)); // fed
								final int newObjId = replacedObjIds.getOrDefault(rs2.getInt(9), -1);
								if (newObjId < 0)
								{
									continue;
								}
								ps.setInt(9, newObjId); // ownerId
								ps.setString(10, rs2.getString(10)); // restore
								ps.setInt(11, rs2.getInt(11)); // evolveLevel
								ps.setInt(12, rs2.getInt(12)); // transformId
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// character_variables
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("character_variables");
						{
							String columns[] =
							{
								"charId",
								"STAT_POINTS",
								"STAT_ELIXIR",
								"STAT_STR",
								"STAT_DEX",
								"STAT_CON",
								"STAT_INT",
								"STAT_WIT",
								"STAT_MEN",
								"FAVORITE_TELEPORTS",
								"RESURRECTION_COUNT_FREE",
								"RESURRECTION_COUNT_ADENA",
								"RESURRECTION_COUNT_LCOIN",
								"FREE_RAID_TELEPORT_USED",
								"SPECIAL_HUNTING_ZONE_REMAINING_TIME_1",
								"SPECIAL_HUNTING_ZONE_EXTENDED_TIME_1",
								"SPECIAL_HUNTING_ZONE_REMAINING_TIME_4",
								"SPECIAL_HUNTING_ZONE_EXTENDED_TIME_4",
								"SPECIAL_HUNTING_ZONE_REMAINING_TIME_11",
								"SPECIAL_HUNTING_ZONE_EXTENDED_TIME_11",
								"SPECIAL_HUNTING_ZONE_REMAINING_TIME_12",
								"SPECIAL_HUNTING_ZONE_EXTENDED_TIME_12",
								"SPECIAL_HUNTING_ZONE_REMAINING_TIME_18",
								"SPECIAL_HUNTING_ZONE_EXTENDED_TIME_18",
								"TRANSCENDENT_ZONE_USED",
								"TRANSCENDENT_ZONE_RESETTED",
								"MAX_LEVEL",
								"LANG",
								"CLAN_DONATIONS_MADE",
								"CLAN_JOINED_TODAY",
								"UI_KEY_MAPPING",
								"HONOR_COINS",
								"DISCORD_VERIFICATION_CODE",
								"DISCORD_NAME",
								"DISCORD_IS_NAME_VERIFIED",
								"PURGE_DATA_POINTS_1",
								"PURGE_DATA_CURRENT_KEYS_1",
								"PURGE_DATA_TOTAL_KEYS_1",
								"PURGE_DATA_POINTS_2",
								"PURGE_DATA_CURRENT_KEYS_2",
								"PURGE_DATA_TOTAL_KEYS_2",
								"PURGE_DATA_POINTS_3",
								"PURGE_DATA_CURRENT_KEYS_3",
								"PURGE_DATA_TOTAL_KEYS_3",
								"PURGE_DATA_POINTS_4",
								"PURGE_DATA_CURRENT_KEYS_4",
								"PURGE_DATA_TOTAL_KEYS_4",
								"PURGE_DATA_POINTS_5",
								"PURGE_DATA_CURRENT_KEYS_5",
								"PURGE_DATA_TOTAL_KEYS_5",
								"PURGE_DATA_POINTS_6",
								"PURGE_DATA_CURRENT_KEYS_6",
								"PURGE_DATA_TOTAL_KEYS_6",
								"PURGE_DATA_POINTS_7",
								"PURGE_DATA_CURRENT_KEYS_7",
								"PURGE_DATA_TOTAL_KEYS_7",
								"PURGE_DATA_POINTS_8",
								"PURGE_DATA_CURRENT_KEYS_8",
								"PURGE_DATA_TOTAL_KEYS_8",
								"TARBA_TELEPORTATION_USED",
								"UNCLAIMED_OLYMPIAD_POINTS",
								"visualHairId",
								"visualHairColorId",
								"visualFaceId",
								"HennaDuration1",
								"HennaDuration2",
								"HennaDuration3",
								"HennaDuration4",
								"DyePotentialDailyStep",
								"DyePotentialDailyCount",
								"SHOW_CLOAK_WITH_COSTUME",
							};
							String sel = columns[0];
							for (int i = 1; i < columns.length; i++)
							{
								sel += "," + columns[i];
							}
							rs2 = conn2.prepareStatement("SELECT " + sel + " FROM character_variables").executeQuery();
							final ResultSetMetaData meta2 = rs2.getMetaData();
							final int columnCount2 = meta2.getColumnCount();
							String temp = "?";
							for (int i = 1; i < columnCount2; i++)
							{
								temp += ",?";
							}
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO character_variables (" + sel + ") VALUES (" + temp + ")");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								for (int i = 2; i <= columnCount2; i++)
								{
									ps.setString(i, rs2.getString(i)); // data
								}
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// player_vengeances
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("player_vengeances");
						connMain.prepareStatement("DELETE FROM player_vengeances").execute();
						// pledge_applicant
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("pledge_applicant");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM pledge_applicant").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO pledge_applicant VALUES (?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, replacedObjIds.get(rs2.getInt(2))); // clanId
								ps.setInt(3, rs2.getInt(3)); // karma
								ps.setString(4, rs2.getString(4)); // message
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// pledge_recruit
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("pledge_recruit");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM pledge_recruit").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO pledge_recruit VALUES (?,?,?,?,?,?)");
							while (rs2.next())
							{
								final int newObjId = replacedObjIds.getOrDefault(rs2.getInt(1), -1);
								if (newObjId < 0)
								{
									continue;
								}
								ps.setInt(1, newObjId); // clanId
								ps.setInt(2, rs2.getInt(2)); // karma
								ps.setString(3, rs2.getString(3)); // information
								ps.setString(4, rs2.getString(4)); // detailed_information
								ps.setInt(5, rs2.getInt(5)); // application_type
								ps.setInt(6, rs2.getInt(6)); // recruit_type
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// pledge_waiting_list
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("pledge_waiting_list");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM pledge_waiting_list").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO pledge_waiting_list VALUES (?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // char_id
								ps.setInt(2, rs2.getInt(2)); // karma
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// prime_shop_limits_used
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("prime_shop_limits_used");
						connMain.prepareStatement("DELETE FROM prime_shop_limits_used").execute();
						// promo_codes
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("promo_codes");
						connMain.prepareStatement("DELETE FROM promo_codes").execute();
						// punishments
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("punishments");
						{
							List<String> bannedAccountsMain = new ArrayList<>();
							rsMain = connMain.prepareStatement("SELECT `key` FROM punishments WHERE `affect`='ACCOUNT'").executeQuery();
							while (rsMain.next())
							{
								bannedAccountsMain.add(rsMain.getString(1));
							}
							rs2 = conn2.prepareStatement("SELECT `key`, `affect`, `type`, `expiration`, `reason`, `punishedBy` FROM punishments").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO punishments (`key`, `affect`, `type`, `expiration`, `reason`, `punishedBy`) VALUES (?,?,?,?,?,?)");
							while (rs2.next())
							{
								String key = rs2.getString(1);
								final String affect = rs2.getString(2);
								if (affect.equals("ACCOUNT"))
								{
									if (bannedAccountsMain.contains(key))
									{
										continue;
									}
								}
								else if (affect.equals("CHARACTER"))
								{
									key = String.valueOf(replacedObjIds.get(Integer.parseInt(key)));
									if ((key == null) || key.equals("null"))
									{
										continue;
									}
								}
								else
								{
									continue;
								}
								ps.setString(1, key); // key
								ps.setString(2, affect); // affect
								ps.setString(3, rs2.getString(3)); // type
								ps.setLong(4, rs2.getLong(4)); // expiration
								ps.setString(5, rs2.getString(5)); // reason
								ps.setString(6, rs2.getString(6)); // punishedBy
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// random_craft
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("random_craft");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM random_craft").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO random_craft VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								for (int i = 2; i <= 24; i++)
								{
									ps.setInt(i, rs2.getInt(i)); // data
								}
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// ranking_statistics
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("ranking_statistics");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM ranking_statistics").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO ranking_statistics VALUES (?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setLong(2, rs2.getLong(2)); // time
								ps.setInt(3, rs2.getInt(3)); // server_rank
								ps.setInt(4, rs2.getInt(4)); // race_rank
								ps.setLong(5, rs2.getLong(5)); // exp_earned
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// rankings_clan
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("rankings_clan");
						connMain.prepareStatement("DELETE FROM rankings_clan").execute();
						// rankings_level
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("rankings_level");
						connMain.prepareStatement("DELETE FROM rankings_level").execute();
						// rankings_pet
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("rankings_pet");
						connMain.prepareStatement("DELETE FROM rankings_pet").execute();
						// rankings_pvp
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("rankings_pvp");
						connMain.prepareStatement("DELETE FROM rankings_pvp").execute();
						// siege_clans
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("siege_clans");
						connMain.prepareStatement("DELETE FROM siege_clans").execute();
						// steady_box
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("steady_box");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM steady_box").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO steady_box VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setInt(2, rs2.getInt(2)); // steady_boxes_owned
								ps.setInt(3, rs2.getInt(3)); // monster_hunting_point
								ps.setInt(4, rs2.getInt(4)); // player_hunting_point
								ps.setInt(5, rs2.getInt(5)); // pending_blox_slot_id
								ps.setLong(6, rs2.getLong(6)); // box_open_time
								for (int i = 7; i < 14; i += 2) // 4 boxes
								{
									ps.setInt(i, rs2.getInt(i)); // box_i_state
									ps.setInt(i + 1, rs2.getInt(i + 1)); // box_i_type
								}
								ps.addBatch();
							}
							ps.executeBatch();
						}
						// toggled_shortcuts
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						start = System.currentTimeMillis();
						System.out.println("toggled_shortcuts");
						{
							rs2 = conn2.prepareStatement("SELECT * FROM toggled_shortcuts").executeQuery();
							PreparedStatement ps = connMain.prepareStatement("INSERT INTO toggled_shortcuts VALUES (?,?)");
							while (rs2.next())
							{
								ps.setInt(1, replacedObjIds.get(rs2.getInt(1))); // charId
								ps.setString(2, rs2.getString(2)); // shortcuts
								ps.addBatch();
							}
							ps.executeBatch();
						}
						System.out.println((System.currentTimeMillis() - start) + " ms. elapsed.");
						connMain.close();
						conn2.close();
						System.exit(0);
					}
					catch (Exception e)
					{
						e.printStackTrace();
					}
					try
					{
						database2.close();
						LOGGER.info("2 Database: closed");
					}
					catch (Exception e)
					{
						LOGGER.severe("2 Database: There was a problem closing the data source. " + e);
					}
				}
			}
			else
			{
				activeChar.setAdminDebug(!activeChar.getAdminDebug());
				activeChar.sendMessage("AdminDebug: " + activeChar.getAdminDebug());
			}
		}
		return true;
	}
	
	@Override
	public String[] getAdminCommandList()
	{
		return ADMIN_COMMANDS;
	}
	
	private static class Item
	{
		private final int	_objectId;
		private final long	_count;
		private final long	_price;
		
		public Item(int objectId, long count, long price)
		{
			_objectId = objectId;
			_count = count;
			_price = price;
		}
		
		public boolean addToTradeList(TradeList list)
		{
			if ((MAX_ADENA / _count) < _price)
			{
				return false;
			}
			list.addItem(_objectId, _count, _price);
			return true;
		}
	}
}
