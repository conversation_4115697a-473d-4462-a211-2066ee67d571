-- Create character_variables table if not exists
-- This script ensures the character_variables table exists for the flexible player variables system

-- Create character_variables table
CREATE TABLE IF NOT EXISTS `character_variables` (
  `charId` int(10) unsigned NOT NULL,
  `var` varchar(255) NOT NULL,
  `val` text NOT NULL,
  PRIMARY KEY (`charId`, `var`),
  <PERSON>EY `idx_charId` (`charId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Note: This table replaces the old player_variables table with a flexible key-value structure
-- The old player_variables table can be dropped after migration is complete and verified
