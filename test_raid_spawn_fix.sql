-- Test script to verify raid_respawns table structure
-- This helps debug the RaidSpawnManager StatSet.getLong error

-- Check if raid_respawns table exists and show its structure
DESCRIBE raid_respawns;

-- Show sample data from raid_respawns table
SELECT * FROM raid_respawns LIMIT 5;

-- Check for any NULL values in critical columns
SELECT 
    id,
    deathTime,
    respawnTime,
    currentHp,
    currentMp
FROM raid_respawns 
WHERE deathTime IS NULL 
   OR respawnTime IS NULL 
   OR currentHp IS NULL 
   OR currentMp IS NULL;

-- Show data types for critical columns
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'raid_respawns' 
  AND TABLE_SCHEMA = DATABASE()
  AND COLUMN_NAME IN ('deathTime', 'respawnTime', 'currentHp', 'currentMp');
